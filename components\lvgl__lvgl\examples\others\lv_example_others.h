/**
 * @file lv_example_others.h
 *
 */

#ifndef LV_EXAMPLE_OTHERS_H
#define LV_EXAMPLE_OTHERS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "snapshot/lv_example_snapshot.h"
#include "monkey/lv_example_monkey.h"
#include "gridnav/lv_example_gridnav.h"
#include "fragment/lv_example_fragment.h"
#include "imgfont/lv_example_imgfont.h"
#include "msg/lv_example_msg.h"
#include "ime/lv_example_ime_pinyin.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_OTHERS_H*/
