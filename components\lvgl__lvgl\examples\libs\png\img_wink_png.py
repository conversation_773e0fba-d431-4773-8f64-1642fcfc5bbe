
img_wink_png_map = bytes([
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52,
  0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0x00, 0x32, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1e, 0x3f, 0x88,
  0xb1, 0x00, 0x00, 0x00, 0x06, 0x62, 0x4b, 0x47, 0x44, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0xa0,
  0xbd, 0xa7, 0x93, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00,
  0x00, 0x0b, 0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x07, 0x74, 0x49, 0x4d, 0x45,
  0x07, 0xe5, 0x05, 0x07, 0x0c, 0x1b, 0x26, 0xad, 0x4b, 0x20, 0x5b, 0x00, 0x00, 0x00, 0x1d, 0x69,
  0x54, 0x58, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x43,
  0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x20, 0x77, 0x69, 0x74, 0x68, 0x20, 0x47, 0x49, 0x4d, 0x50,
  0x64, 0x2e, 0x65, 0x07, 0x00, 0x00, 0x13, 0x8a, 0x49, 0x44, 0x41, 0x54, 0x68, 0xde, 0xad, 0x9a,
  0x69, 0x94, 0x5d, 0xd5, 0x75, 0xe7, 0x7f, 0xe7, 0xdc, 0x7b, 0xdf, 0x7d, 0x43, 0xd5, 0xab, 0xe9,
  0x55, 0x49, 0x05, 0xaa, 0xd2, 0x80, 0x10, 0x12, 0x48, 0xa8, 0x24, 0x04, 0x66, 0x72, 0x6c, 0xec,
  0xd8, 0xd0, 0x6e, 0x27, 0xc4, 0x6d, 0x4c, 0x08, 0x21, 0xdd, 0xed, 0x24, 0xbd, 0x1c, 0x67, 0x39,
  0xcb, 0x6d, 0x33, 0xc7, 0x6e, 0x9b, 0xc6, 0x18, 0x64, 0x90, 0x43, 0xda, 0xb1, 0x1d, 0x7f, 0x31,
  0x89, 0x93, 0xb8, 0x33, 0x38, 0x84, 0x15, 0x63, 0xe3, 0x38, 0x84, 0x80, 0x99, 0x67, 0x24, 0x10,
  0x83, 0x06, 0x84, 0x84, 0x54, 0x52, 0x95, 0x6a, 0x7e, 0xf5, 0xa6, 0x3b, 0x9c, 0xb3, 0xfb, 0xc3,
  0xbd, 0x6f, 0x28, 0xa1, 0xac, 0x0e, 0x8e, 0x6b, 0xad, 0xb3, 0x6e, 0xd5, 0x7b, 0xa7, 0xce, 0xd9,
  0xff, 0xb3, 0xa7, 0xff, 0xde, 0xf7, 0x28, 0x7e, 0x8e, 0x9f, 0x97, 0x6f, 0x18, 0xe2, 0xec, 0x3b,
  0x8f, 0x03, 0xf0, 0xe0, 0xd5, 0x5b, 0x19, 0x1e, 0xdd, 0xa7, 0x4b, 0xab, 0x95, 0x5a, 0x98, 0xee,
  0xe9, 0x6d, 0xcc, 0x95, 0xcf, 0xcb, 0xf5, 0xc8, 0xe6, 0x6c, 0x97, 0x3e, 0x5d, 0x3b, 0x76, 0xd0,
  0x51, 0xaa, 0x28, 0x56, 0x10, 0xc3, 0xa2, 0x89, 0xd5, 0x74, 0x58, 0xe3, 0x50, 0xa3, 0xa6, 0x5e,
  0x15, 0xed, 0xbf, 0x30, 0x30, 0x5c, 0x38, 0x52, 0x9f, 0x1a, 0x8f, 0xe7, 0xaa, 0x3d, 0xb2, 0xed,
  0xee, 0xe3, 0xd2, 0x5e, 0x7f, 0x19, 0x67, 0xdf, 0x39, 0xf9, 0xae, 0x64, 0x52, 0xef, 0x66, 0xf2,
  0x4b, 0xd7, 0x0d, 0xb3, 0x65, 0xc7, 0x31, 0x00, 0x7e, 0x70, 0xee, 0x6a, 0xb5, 0xed, 0x13, 0xc7,
  0xfd, 0x58, 0x7a, 0x46, 0x95, 0xa9, 0x7c, 0xa2, 0xab, 0xa4, 0xaf, 0xcc, 0xe4, 0xe3, 0xb3, 0xf3,
  0xbd, 0x1a, 0x37, 0x23, 0x80, 0x80, 0xa8, 0xe4, 0x69, 0x01, 0x63, 0x51, 0x06, 0xc4, 0x28, 0x6c,
  0x03, 0x1a, 0x65, 0xa1, 0x51, 0xd3, 0xc7, 0xeb, 0x55, 0x1e, 0x08, 0x43, 0xef, 0xaf, 0x5d, 0x47,
  0xbf, 0x30, 0xb7, 0xa0, 0xcb, 0x5b, 0xef, 0x9e, 0x8a, 0x9a, 0xfb, 0xed, 0xba, 0x6e, 0x19, 0x9b,
  0x77, 0x4c, 0xfe, 0xe2, 0x80, 0xec, 0xff, 0xf6, 0xc7, 0x88, 0x8f, 0xfd, 0x88, 0xf5, 0x5f, 0x49,
  0xf6, 0x78, 0xe3, 0xcb, 0x5d, 0x5d, 0x5e, 0x2e, 0x7b, 0x6e, 0xb6, 0x18, 0x7f, 0x26, 0xdf, 0x6b,
  0xff, 0x4b, 0xd7, 0xa0, 0x42, 0x39, 0x2e, 0x38, 0x59, 0x20, 0x83, 0x68, 0x0f, 0x94, 0x87, 0xd2,
  0x39, 0x40, 0x83, 0x44, 0x88, 0xa9, 0x83, 0x09, 0xc0, 0x84, 0xa8, 0x30, 0x40, 0x05, 0x01, 0x84,
  0x06, 0x22, 0x45, 0xa3, 0x6c, 0xa9, 0x2e, 0xea, 0xb7, 0xea, 0x81, 0xf7, 0x4d, 0x1b, 0xdb, 0xfb,
  0x0e, 0x8e, 0x17, 0x0f, 0x5f, 0xf2, 0xdd, 0x23, 0x31, 0xc0, 0xab, 0x5f, 0x7b, 0x2f, 0x67, 0xdd,
  0xf8, 0xd8, 0x7f, 0x1c, 0xc8, 0xae, 0x1b, 0x47, 0xd8, 0xfc, 0xb5, 0xc3, 0x00, 0xfc, 0xf4, 0xd2,
  0x62, 0x66, 0xed, 0x07, 0x33, 0x9b, 0xb3, 0xdd, 0xe6, 0xf3, 0xdd, 0x83, 0x72, 0x55, 0xae, 0x17,
  0x54, 0xa6, 0x1b, 0x71, 0xfb, 0x20, 0xbb, 0x12, 0xd5, 0xb3, 0x09, 0x95, 0x3f, 0x1d, 0x72, 0x23,
  0xe0, 0xf5, 0x82, 0xd2, 0xed, 0x85, 0x24, 0x86, 0x60, 0x06, 0xea, 0x87, 0x91, 0xea, 0x5e, 0x98,
  0x7f, 0x05, 0x16, 0x0f, 0x41, 0x75, 0x0e, 0x55, 0xab, 0xa0, 0x02, 0x4b, 0x54, 0x83, 0xc5, 0xb2,
  0x3a, 0xdc, 0x08, 0xdd, 0xdb, 0xe6, 0xe6, 0xd5, 0x8f, 0x37, 0x7d, 0x7d, 0x61, 0x1c, 0xe0, 0xa5,
  0xeb, 0x57, 0xb0, 0xe5, 0xae, 0x23, 0x3f, 0x3f, 0x90, 0x5d, 0xd7, 0x95, 0xd8, 0xbc, 0x63, 0x3a,
  0xb5, 0xdb, 0x81, 0xc1, 0xbe, 0x11, 0xf9, 0xaf, 0x85, 0x12, 0x37, 0x15, 0x06, 0x28, 0x39, 0xd9,
  0x02, 0xf8, 0xa3, 0xd0, 0x77, 0x1e, 0xaa, 0xff, 0x62, 0xc8, 0xad, 0x04, 0x31, 0xe9, 0xb0, 0x24,
  0xf6, 0x74, 0x92, 0xed, 0x54, 0x3a, 0x50, 0x50, 0x3f, 0x02, 0x33, 0xcf, 0x20, 0x53, 0xcf, 0xc3,
  0xec, 0x01, 0x58, 0x9c, 0x46, 0x87, 0x50, 0xaf, 0xc2, 0x62, 0xc5, 0xf9, 0x49, 0xad, 0xae, 0xef,
  0x58, 0x73, 0x6b, 0xf9, 0x31, 0x80, 0x9d, 0x37, 0x0c, 0x33, 0x76, 0xe7, 0xb1, 0x77, 0x0f, 0x64,
  0xd7, 0xf5, 0x7d, 0x6c, 0xbe, 0x6b, 0x0e, 0x80, 0x3d, 0xb7, 0x0e, 0x9c, 0xd5, 0x73, 0x8a, 0xfa,
  0x52, 0xf7, 0x32, 0x75, 0xa5, 0xdf, 0x25, 0x90, 0x1d, 0x81, 0xbe, 0x8b, 0x50, 0x43, 0x97, 0x42,
  0x66, 0x28, 0x39, 0x6d, 0x31, 0x27, 0x59, 0x45, 0xda, 0xcf, 0x14, 0xa0, 0x12, 0x83, 0x60, 0xdb,
  0xf3, 0xb5, 0x87, 0x8a, 0xab, 0xc8, 0xec, 0x0b, 0xc8, 0xb1, 0xa7, 0x60, 0xe2, 0x15, 0x54, 0xb5,
  0x82, 0xc4, 0x9a, 0x85, 0x05, 0xa6, 0x2a, 0x55, 0xe7, 0xe6, 0xaf, 0xfd, 0xf0, 0xb2, 0xef, 0x7d,
  0xfb, 0xc5, 0x1f, 0xc4, 0x3b, 0x3f, 0xd7, 0xcb, 0xd8, 0xdd, 0xf3, 0xff, 0x7e, 0x20, 0x2f, 0xdf,
  0x50, 0xe2, 0xec, 0x3b, 0x13, 0x4d, 0xec, 0xbb, 0xbd, 0xff, 0xe2, 0xbe, 0x11, 0xe7, 0xae, 0xc2,
  0xa0, 0x3e, 0xdf, 0xcb, 0x6b, 0xe8, 0xda, 0x8c, 0x5a, 0x76, 0x39, 0x14, 0xcf, 0x4e, 0x65, 0x3c,
  0x19, 0x00, 0x8b, 0x12, 0x69, 0x09, 0xac, 0xc4, 0x22, 0x62, 0x81, 0xb6, 0xb6, 0x94, 0x98, 0x44,
  0x6b, 0x62, 0x12, 0xa0, 0xda, 0x43, 0xa2, 0x05, 0x98, 0x7c, 0x16, 0x79, 0xfb, 0x31, 0x98, 0x3c,
  0x80, 0xb2, 0x0e, 0xd5, 0x2a, 0x71, 0xb9, 0xa2, 0xbf, 0xf2, 0xe1, 0xff, 0x75, 0xda, 0x57, 0x5f,
  0x63, 0x97, 0xd9, 0x79, 0xfd, 0x72, 0xc6, 0xee, 0x9a, 0xf8, 0xff, 0x03, 0xd9, 0x79, 0xdd, 0x10,
  0x63, 0x3b, 0x92, 0xd0, 0xba, 0xff, 0x8e, 0xfe, 0x4b, 0xfa, 0x56, 0x39, 0xdf, 0x28, 0x94, 0xdc,
  0x8d, 0xae, 0xef, 0x42, 0xdf, 0xc5, 0xa8, 0xe1, 0x2b, 0xc0, 0x1f, 0x6e, 0x0b, 0xd0, 0x71, 0xfa,
  0x0a, 0x0b, 0xa9, 0xd0, 0x4b, 0x4e, 0x5e, 0x92, 0xcf, 0x15, 0x6d, 0xe1, 0x45, 0x2c, 0x8a, 0x14,
  0x60, 0x13, 0x14, 0x02, 0xca, 0x81, 0x85, 0x03, 0x70, 0xe8, 0x11, 0xe4, 0xd0, 0x73, 0x28, 0xeb,
  0xd0, 0x68, 0x88, 0x99, 0x9d, 0x77, 0x76, 0x8c, 0x7c, 0xa9, 0x72, 0x13, 0xc0, 0xce, 0x9b, 0x4f,
  0x63, 0xec, 0x8e, 0x37, 0xff, 0x6d, 0x20, 0xaf, 0x7c, 0x69, 0x3d, 0x9b, 0x6e, 0x7d, 0x23, 0xd1,
  0xc4, 0xf6, 0xd2, 0x7b, 0xfa, 0x47, 0xf5, 0x9f, 0x77, 0x2d, 0xf3, 0xd6, 0x3b, 0x9e, 0x03, 0xfd,
  0xef, 0x47, 0x9d, 0x72, 0x25, 0x78, 0xfd, 0x20, 0x31, 0xda, 0x71, 0x00, 0x41, 0x4c, 0x94, 0x0a,
  0xd3, 0x71, 0xe2, 0x1d, 0xc2, 0xa9, 0xa6, 0xbf, 0x88, 0x41, 0x3a, 0xb5, 0xd0, 0xfa, 0xbc, 0x03,
  0x88, 0x4d, 0xd7, 0x50, 0x1a, 0x82, 0x39, 0xe4, 0xd0, 0xa3, 0xb0, 0xff, 0x31, 0xb0, 0x2e, 0x41,
  0x40, 0x70, 0x7c, 0x46, 0x6f, 0x5f, 0xfd, 0xbf, 0x2b, 0xb7, 0xc8, 0x93, 0xb0, 0xff, 0xe0, 0xe7,
  0x38, 0xfd, 0xea, 0xbb, 0x4f, 0x0e, 0x44, 0x9e, 0x06, 0x75, 0x3e, 0xec, 0xbf, 0x63, 0x68, 0x4d,
  0xf7, 0x72, 0xfe, 0xae, 0x6f, 0xa5, 0x7f, 0x8e, 0x76, 0x25, 0xf1, 0x87, 0x53, 0xaf, 0x49, 0x40,
  0xd8, 0x08, 0x9d, 0xcd, 0x30, 0x77, 0xe8, 0x6d, 0x4c, 0x14, 0xd2, 0x37, 0x3c, 0x88, 0x52, 0xd2,
  0x21, 0x98, 0x39, 0x41, 0xf8, 0x26, 0xc0, 0x7f, 0x43, 0x78, 0x39, 0x11, 0x4c, 0xf2, 0x14, 0x80,
  0xb8, 0x06, 0x6f, 0xfd, 0x0c, 0xde, 0x7c, 0x0a, 0xf0, 0x68, 0x04, 0xcc, 0x1e, 0x3e, 0xea, 0x5d,
  0xbf, 0x61, 0xfb, 0xc2, 0x3d, 0x6f, 0x5c, 0xeb, 0xa9, 0xf5, 0x5f, 0x8f, 0x5a, 0x26, 0xd1, 0x8a,
  0x8f, 0xbb, 0x6f, 0x2e, 0xa1, 0xce, 0x87, 0xdd, 0x37, 0xf6, 0xf4, 0xe6, 0x7a, 0xf8, 0x4a, 0xdf,
  0x68, 0x36, 0x01, 0xd1, 0x75, 0x26, 0x6a, 0xf9, 0xc7, 0x50, 0x6e, 0x2f, 0xd8, 0x06, 0xda, 0x85,
  0xf1, 0x5d, 0x3b, 0xf9, 0xfe, 0x1f, 0x7e, 0x95, 0x3f, 0xbe, 0xe6, 0xb3, 0x4c, 0xee, 0x7f, 0x13,
  0x4c, 0x80, 0xb2, 0x01, 0x2a, 0x7d, 0x62, 0x1b, 0x88, 0x69, 0x20, 0x36, 0x00, 0x69, 0x80, 0x0d,
  0xc0, 0x86, 0x1d, 0x23, 0x48, 0x86, 0x49, 0x9f, 0x36, 0x99, 0x23, 0x26, 0x40, 0x6c, 0x88, 0x98,
  0x10, 0xe2, 0x46, 0x22, 0xd8, 0xc8, 0x36, 0x18, 0x5c, 0x05, 0x62, 0xf1, 0x33, 0xf4, 0x9f, 0xb2,
  0x2c, 0xba, 0xe1, 0xa9, 0x4f, 0x97, 0xc6, 0xd6, 0x7f, 0x3d, 0x92, 0x97, 0x3e, 0x37, 0xc0, 0x12,
  0x20, 0xaf, 0x6f, 0xbf, 0x90, 0x8d, 0x77, 0x4c, 0xf3, 0xd3, 0x4b, 0xbb, 0xbd, 0xc2, 0x90, 0x7f,
  0x79, 0xef, 0x4a, 0xef, 0x6a, 0x27, 0xa3, 0x21, 0xb3, 0x1c, 0x55, 0xfa, 0x00, 0x64, 0x96, 0x23,
  0xa6, 0x86, 0x92, 0x64, 0xd3, 0x87, 0xee, 0xf9, 0x1b, 0x0e, 0xbc, 0xf0, 0x0a, 0xc3, 0x83, 0x73,
  0x44, 0xf3, 0x7b, 0xc0, 0xd4, 0x11, 0xdb, 0x48, 0x47, 0x80, 0x48, 0x08, 0xd2, 0x21, 0x6c, 0x4b,
  0xe0, 0xce, 0xbf, 0xc3, 0xb6, 0xf0, 0x26, 0x15, 0xde, 0x86, 0xe9, 0x77, 0x8d, 0xf6, 0x50, 0x0a,
  0xd6, 0x5c, 0x08, 0x19, 0x8d, 0x52, 0x90, 0xf5, 0x39, 0x63, 0xf5, 0x68, 0xfd, 0x36, 0x80, 0x2d,
  0x77, 0xcf, 0x2c, 0x05, 0xb2, 0xe1, 0xa6, 0x27, 0x01, 0xd8, 0xf0, 0xab, 0xbd, 0xc3, 0x85, 0x41,
  0xbd, 0x23, 0xdb, 0xe3, 0x23, 0x78, 0xd0, 0xbd, 0x11, 0xba, 0xb7, 0x82, 0xa9, 0x82, 0x6d, 0xa0,
  0x1c, 0x61, 0xdf, 0x53, 0xcf, 0x72, 0xf4, 0x8d, 0xfd, 0xfc, 0xca, 0x15, 0x9a, 0x2b, 0x3e, 0xbd,
  0x8e, 0xe5, 0xab, 0x4a, 0x40, 0x08, 0x92, 0x0e, 0xdb, 0x00, 0xd3, 0xe8, 0x10, 0xbe, 0x53, 0xc0,
  0xf6, 0x48, 0x34, 0x76, 0xc2, 0x77, 0xa6, 0x91, 0x1e, 0x40, 0x73, 0xad, 0x44, 0xa3, 0x2a, 0x9b,
  0x85, 0xe5, 0xa7, 0x03, 0x06, 0xc7, 0x81, 0x62, 0xb7, 0x5c, 0xb0, 0xf7, 0x0b, 0xdd, 0xbf, 0xdf,
  0xcc, 0x75, 0x00, 0xfa, 0xf5, 0x3b, 0xce, 0x4f, 0xf2, 0xc6, 0xb5, 0x3d, 0x59, 0x1b, 0x36, 0x3e,
  0xdd, 0x37, 0x9a, 0x2d, 0x89, 0x08, 0xe4, 0x4e, 0x41, 0xf5, 0x5d, 0x90, 0x2e, 0xdc, 0x40, 0xd9,
  0x10, 0x1c, 0xc3, 0x6b, 0x8f, 0xbf, 0xc4, 0x59, 0x1b, 0xa6, 0xd9, 0xf4, 0xc1, 0x8b, 0x29, 0x9d,
  0xf7, 0x07, 0x78, 0x3d, 0x2b, 0x52, 0x61, 0x52, 0x00, 0x9d, 0x26, 0x74, 0x52, 0xd3, 0x09, 0x96,
  0x9e, 0xbe, 0x09, 0xda, 0xda, 0x6b, 0x3d, 0x1b, 0x09, 0x00, 0x09, 0x50, 0x12, 0x02, 0x21, 0xaa,
  0x74, 0x2a, 0xe8, 0xc4, 0x25, 0x5c, 0x87, 0xfe, 0xc1, 0x7e, 0x73, 0xcd, 0x7d, 0x57, 0xad, 0x1c,
  0x68, 0x26, 0x6c, 0x77, 0xc3, 0xcd, 0x4f, 0x03, 0xd0, 0xbf, 0xba, 0xbb, 0x94, 0xef, 0x33, 0x9f,
  0xd7, 0x9e, 0x83, 0xe0, 0x43, 0x7e, 0x2d, 0xf8, 0xa7, 0xa0, 0x4c, 0x2d, 0x71, 0x44, 0x25, 0x44,
  0x0b, 0x55, 0xa4, 0x72, 0x80, 0xb5, 0xdb, 0xce, 0x64, 0x60, 0xe3, 0xaf, 0x22, 0xda, 0x4f, 0x4f,
  0x51, 0x3a, 0x9c, 0xd9, 0xb4, 0x9d, 0xb7, 0x95, 0x3f, 0x52, 0x87, 0xb6, 0x16, 0x6b, 0x63, 0xac,
  0x89, 0x40, 0x2c, 0x5a, 0x83, 0xd6, 0xd2, 0xfe, 0x3f, 0x3a, 0x02, 0x45, 0xcb, 0xf1, 0xd3, 0x40,
  0xe1, 0x79, 0xe0, 0x29, 0x08, 0xc0, 0xd1, 0xe0, 0xfb, 0x9c, 0x35, 0xb6, 0x6e, 0xe6, 0x93, 0xc0,
  0x0e, 0x00, 0x07, 0x60, 0xe7, 0xb5, 0x3d, 0xae, 0x93, 0x91, 0x4f, 0x2d, 0xdb, 0x50, 0xb8, 0x14,
  0xad, 0xc1, 0x2f, 0xa1, 0x4a, 0xef, 0x47, 0x39, 0x99, 0xc4, 0x61, 0x89, 0xd1, 0x8e, 0xe5, 0xf0,
  0xee, 0x37, 0xc9, 0x98, 0x43, 0xac, 0x7e, 0xcf, 0x2f, 0x93, 0xe9, 0x1e, 0x02, 0x13, 0x82, 0x8d,
  0x41, 0xa2, 0x64, 0x10, 0x81, 0x8d, 0x11, 0x1b, 0xa7, 0xd9, 0xbe, 0x63, 0xd8, 0x88, 0xe9, 0xa3,
  0xd3, 0x3c, 0xf3, 0xc0, 0x8b, 0x3c, 0x75, 0xff, 0xf3, 0xbc, 0xf1, 0xcc, 0x1e, 0x2a, 0xb3, 0x0b,
  0x14, 0xba, 0x1c, 0x72, 0x79, 0x85, 0x52, 0x06, 0x4d, 0x8c, 0x22, 0x42, 0x49, 0x9c, 0xac, 0x23,
  0x21, 0xd8, 0x28, 0xd9, 0xa7, 0x32, 0x0e, 0x8b, 0x53, 0x60, 0x54, 0x33, 0x7d, 0xf9, 0x5a, 0x8b,
  0xda, 0xdc, 0x7d, 0xf6, 0x7d, 0xf7, 0xbe, 0x7e, 0x2c, 0x74, 0x13, 0x6d, 0x14, 0x7c, 0xbf, 0xcb,
  0xfe, 0x8e, 0xce, 0x38, 0x88, 0x38, 0x90, 0x29, 0x81, 0xbf, 0x0c, 0x69, 0x6a, 0x43, 0x2c, 0x28,
  0x85, 0x69, 0xcc, 0xb3, 0x62, 0xec, 0x42, 0xba, 0x4b, 0x43, 0xd8, 0xb8, 0xb6, 0x24, 0xd9, 0xc9,
  0x92, 0x70, 0x6a, 0x96, 0x84, 0x54, 0xad, 0x85, 0xb9, 0xa9, 0x05, 0xfe, 0xf5, 0xff, 0x3e, 0xcc,
  0xe4, 0xbe, 0x37, 0x39, 0x73, 0x4b, 0x0f, 0x5d, 0x3d, 0x1e, 0x8d, 0xd9, 0x79, 0x9e, 0xbb, 0x7f,
  0x1f, 0xe7, 0x7e, 0x64, 0x1b, 0x83, 0x23, 0x7d, 0x2c, 0xcc, 0x87, 0xcc, 0x97, 0x03, 0x72, 0xbe,
  0xd0, 0xdf, 0x2d, 0x29, 0xa0, 0x06, 0x34, 0x8e, 0x23, 0x0b, 0x87, 0x50, 0x19, 0x07, 0x22, 0x01,
  0x23, 0xb8, 0x2e, 0xf8, 0x19, 0xb5, 0x7e, 0xcb, 0xda, 0xbd, 0x97, 0x02, 0xf7, 0xba, 0xcf, 0x7d,
  0x66, 0x98, 0xfa, 0x3c, 0xeb, 0xfa, 0x46, 0xdc, 0xd3, 0x11, 0x05, 0x4e, 0x1e, 0x95, 0x5b, 0x09,
  0xa6, 0x9e, 0x2c, 0x92, 0x0a, 0x6a, 0x03, 0xcb, 0xea, 0x4d, 0xab, 0x00, 0xc1, 0x5a, 0x83, 0xc2,
  0x24, 0x7e, 0x83, 0xc5, 0x88, 0x4e, 0xcc, 0x40, 0xe2, 0x0e, 0xda, 0x61, 0x5b, 0x79, 0x43, 0x8c,
  0xc5, 0xcf, 0x84, 0xbc, 0xef, 0x23, 0x25, 0x06, 0x4a, 0x7d, 0xf8, 0xc5, 0x65, 0x90, 0x29, 0x24,
  0x59, 0x4c, 0x37, 0xa8, 0x4e, 0x2f, 0xf2, 0xf0, 0xcf, 0x8e, 0xf1, 0xd0, 0xb3, 0x65, 0x76, 0xef,
  0x5b, 0xe4, 0x03, 0x5b, 0x35, 0x9f, 0xba, 0x3c, 0x87, 0x23, 0xb5, 0x54, 0x0e, 0x9b, 0xd8, 0x8e,
  0x23, 0xe0, 0x28, 0x50, 0x92, 0x90, 0x00, 0xcd, 0x48, 0x5f, 0x2f, 0x17, 0x00, 0xf7, 0xba, 0x5d,
  0xa5, 0x50, 0x3b, 0x9e, 0xfe, 0xb5, 0x7c, 0xa9, 0x88, 0xa0, 0x40, 0xfb, 0xe0, 0x2f, 0x03, 0x53,
  0x4b, 0xcd, 0x66, 0xa9, 0x50, 0x00, 0x3a, 0x98, 0x62, 0x71, 0x62, 0x9c, 0xc9, 0x63, 0x53, 0x64,
  0x33, 0x2e, 0x2b, 0x46, 0x06, 0x90, 0xae, 0x21, 0xac, 0x93, 0x49, 0xe7, 0x37, 0x35, 0x12, 0x03,
  0x31, 0xd8, 0x90, 0x7c, 0x3e, 0xa4, 0x70, 0xc6, 0xa9, 0x88, 0x69, 0x60, 0xa2, 0x2a, 0x54, 0x66,
  0x70, 0x1c, 0xc3, 0xf4, 0x74, 0x9d, 0xef, 0xde, 0x37, 0xc5, 0x4f, 0x9e, 0xb1, 0x6c, 0x5c, 0x57,
  0xe4, 0xca, 0x8b, 0x84, 0x4d, 0x2b, 0x0d, 0xd1, 0x62, 0x15, 0xa7, 0x4b, 0x81, 0xf2, 0x40, 0x27,
  0x72, 0x88, 0x23, 0x28, 0x27, 0x2d, 0x71, 0x0c, 0xb8, 0x8e, 0xe0, 0x67, 0xec, 0xfa, 0x7f, 0xf8,
  0xf5, 0x95, 0xfd, 0x6e, 0x71, 0x59, 0x84, 0x72, 0xf3, 0x97, 0xb4, 0xa8, 0xb5, 0xe3, 0x83, 0x53,
  0x48, 0x9d, 0xd8, 0x74, 0x38, 0xad, 0xa0, 0x94, 0xa0, 0x17, 0x0f, 0xf2, 0xc4, 0x03, 0x0f, 0xf3,
  0x8f, 0x3f, 0x3b, 0xc2, 0xa4, 0x9c, 0x42, 0x57, 0x06, 0xce, 0xec, 0x1e, 0xe7, 0xaa, 0xcb, 0x37,
  0xd0, 0xbf, 0xe1, 0x5c, 0x6c, 0x26, 0x07, 0x12, 0x27, 0x66, 0x21, 0x21, 0x22, 0x21, 0x22, 0x8d,
  0xc4, 0xf4, 0x62, 0x83, 0x12, 0x83, 0x02, 0x94, 0xeb, 0x51, 0xad, 0x5a, 0xbe, 0xf5, 0xf7, 0x65,
  0x0e, 0xcd, 0xaf, 0xe1, 0x1b, 0xb7, 0x6d, 0x61, 0x53, 0xdf, 0x01, 0xe2, 0x50, 0x13, 0xeb, 0x6e,
  0xa2, 0xb9, 0x37, 0x80, 0xe3, 0x49, 0x1e, 0x91, 0x94, 0xfa, 0x6b, 0x95, 0x24, 0x0c, 0xa5, 0x40,
  0x04, 0xc7, 0x01, 0xd7, 0x91, 0x15, 0x6b, 0x86, 0x67, 0xce, 0x70, 0x73, 0xa7, 0x9e, 0xa7, 0x4d,
  0xf9, 0xd5, 0x73, 0x40, 0x27, 0x84, 0xcd, 0xc9, 0x27, 0x9a, 0x30, 0x01, 0x82, 0x49, 0xf9, 0x8f,
  0x05, 0xa5, 0xd1, 0xf5, 0xc3, 0xfc, 0xe3, 0xf7, 0x7f, 0xc8, 0x9f, 0x3c, 0xd6, 0x83, 0xb7, 0xf5,
  0xcb, 0xac, 0x1e, 0x7b, 0x0f, 0x91, 0x11, 0x1e, 0xdc, 0xf3, 0x1c, 0x6f, 0xfd, 0xcd, 0xb7, 0xf8,
  0xd2, 0x55, 0x4f, 0xd2, 0xb5, 0xe9, 0xa2, 0x94, 0xb6, 0x47, 0x69, 0x24, 0x22, 0x39, 0xd5, 0x44,
  0x7c, 0x04, 0x41, 0xa1, 0x51, 0x1e, 0xfc, 0xdd, 0x3f, 0xcf, 0x12, 0x66, 0xcf, 0x66, 0xfb, 0x6d,
  0x57, 0xd2, 0xb7, 0xf8, 0x02, 0x8b, 0x0b, 0x45, 0x0a, 0xcb, 0x47, 0xc8, 0xd6, 0xde, 0xc6, 0xb7,
  0x71, 0x12, 0xb4, 0x44, 0xb5, 0xfe, 0x17, 0x9d, 0x9a, 0x97, 0x6e, 0x93, 0x2b, 0xa5, 0xd5, 0xf2,
  0xde, 0x5e, 0xbd, 0xca, 0x9d, 0x7c, 0x65, 0x7c, 0xf8, 0x94, 0xf5, 0x3a, 0x9f, 0x14, 0x3d, 0x1a,
  0xb4, 0x9f, 0x94, 0xa5, 0x36, 0x5c, 0xc2, 0x4a, 0x1d, 0x15, 0xf0, 0xec, 0xbf, 0x3e, 0xc3, 0x77,
  0x9f, 0x1d, 0xe0, 0xcc, 0xdf, 0xbc, 0x9d, 0xf7, 0xff, 0xda, 0xc7, 0xc9, 0xf9, 0xc9, 0xb7, 0xb5,
  0xf0, 0xc3, 0x3c, 0xfe, 0xc0, 0x39, 0x7c, 0xe3, 0xfe, 0x4f, 0xf2, 0xc5, 0x55, 0x47, 0xb1, 0xc5,
  0x15, 0x2d, 0x66, 0xac, 0x50, 0x88, 0x48, 0x5a, 0x2d, 0x5a, 0x40, 0xa3, 0x5d, 0x87, 0x57, 0x5e,
  0x9d, 0x67, 0x31, 0x1a, 0xe4, 0xb7, 0x7f, 0xf3, 0x7d, 0x0c, 0x66, 0xea, 0x94, 0xe7, 0x0f, 0x53,
  0x28, 0xe6, 0x61, 0xef, 0x3f, 0x63, 0xe7, 0xf6, 0x42, 0xb7, 0x86, 0xbe, 0xde, 0xb4, 0x10, 0x63,
  0xe9, 0xd0, 0x4b, 0x88, 0xef, 0x40, 0xc6, 0x63, 0xb9, 0x8e, 0xca, 0xf3, 0x6b, 0xfd, 0x6e, 0xb7,
  0xb5, 0x2d, 0xa2, 0x96, 0x64, 0x60, 0x6c, 0x80, 0x22, 0x22, 0x9e, 0x3a, 0xc4, 0xf7, 0x1f, 0x9a,
  0x61, 0xe5, 0xa5, 0x9f, 0xe3, 0xe3, 0x9f, 0xf8, 0x38, 0xa7, 0x75, 0xc1, 0x4a, 0x0f, 0x46, 0x5d,
  0x58, 0xdb, 0x0d, 0x57, 0xfc, 0xc6, 0x65, 0x54, 0x2f, 0xde, 0xce, 0xae, 0xa7, 0x5f, 0x45, 0x3b,
  0x5e, 0xa2, 0x5d, 0x9c, 0x04, 0x8a, 0xd2, 0x9c, 0x28, 0xcd, 0xc1, 0x63, 0x96, 0xcd, 0x1b, 0x86,
  0x38, 0xed, 0xd4, 0x90, 0xf2, 0xde, 0x07, 0xf1, 0x33, 0x35, 0xd4, 0x5b, 0x8f, 0x63, 0x8f, 0xec,
  0x85, 0x48, 0x43, 0xa1, 0xa7, 0x5d, 0x49, 0xb6, 0x34, 0xd2, 0x04, 0xd5, 0xe6, 0xba, 0x4a, 0xe1,
  0x3a, 0x8e, 0xed, 0x75, 0x91, 0x60, 0xb9, 0x9b, 0xcd, 0x75, 0x14, 0x73, 0x61, 0x9b, 0x5a, 0xd0,
  0x0c, 0x9f, 0xf0, 0xe8, 0xd3, 0xfb, 0x31, 0xcb, 0xdf, 0xcb, 0x07, 0x2e, 0xfb, 0x28, 0x6b, 0x7a,
  0xa0, 0xa8, 0xc0, 0xd3, 0x90, 0x77, 0xe0, 0xde, 0x07, 0xc7, 0x79, 0xee, 0x65, 0x4d, 0x39, 0x7a,
  0x2f, 0x7f, 0xff, 0x7a, 0x89, 0xcd, 0x1f, 0xaa, 0x83, 0xca, 0xa4, 0x94, 0x5c, 0x2d, 0x31, 0x0f,
  0xc7, 0xd1, 0x1c, 0x3c, 0x5c, 0x61, 0x59, 0xaf, 0xe2, 0x8c, 0xd5, 0x75, 0xe2, 0xe3, 0xbb, 0x50,
  0xf6, 0x18, 0xba, 0x72, 0x14, 0xe2, 0xe3, 0xe8, 0x5e, 0x17, 0xfa, 0xbb, 0xc1, 0xd7, 0x69, 0x80,
  0x69, 0x96, 0xc8, 0x27, 0xf0, 0xf5, 0xe6, 0x53, 0x04, 0xa5, 0x9c, 0x6e, 0xd7, 0x71, 0x29, 0x2a,
  0xad, 0x12, 0x1c, 0x22, 0x49, 0xb4, 0x6a, 0x51, 0x03, 0x0b, 0xca, 0x42, 0x5c, 0x63, 0xe7, 0xbe,
  0x32, 0x83, 0xeb, 0x2f, 0x61, 0xeb, 0xfa, 0x15, 0x94, 0xdc, 0x64, 0x1f, 0x0d, 0xfc, 0xd5, 0xdf,
  0x1e, 0xe0, 0xc1, 0xfd, 0xfd, 0xbc, 0xbd, 0xff, 0x11, 0xf2, 0xf9, 0x5e, 0xe6, 0xb8, 0x1a, 0x6a,
  0x8f, 0x40, 0xd7, 0x8a, 0x14, 0xc0, 0xd2, 0xea, 0x47, 0x04, 0xb2, 0xae, 0x61, 0xb4, 0x77, 0x81,
  0xa2, 0xaa, 0x53, 0x3e, 0x78, 0x98, 0x4c, 0x21, 0xc6, 0xf1, 0x23, 0x18, 0xea, 0x05, 0xd7, 0x87,
  0xdc, 0x08, 0x2a, 0xbf, 0x0a, 0x2a, 0x7b, 0x90, 0xda, 0xc1, 0xd4, 0xe7, 0x4e, 0x5e, 0x49, 0x2b,
  0x25, 0xe0, 0x78, 0x59, 0xad, 0xb2, 0x5e, 0xbe, 0xb3, 0x44, 0x45, 0xea, 0x28, 0x5b, 0x69, 0x73,
  0x2c, 0x13, 0x40, 0x65, 0x96, 0xd8, 0x1f, 0xa6, 0x67, 0xf9, 0x5a, 0xfa, 0x7c, 0xc8, 0xa4, 0x7e,
  0x77, 0x64, 0x7c, 0x91, 0xe9, 0x28, 0xc7, 0x1f, 0x7d, 0xb6, 0x97, 0xb3, 0x7a, 0x9e, 0xe0, 0xa1,
  0x3f, 0xbb, 0x9c, 0x89, 0x60, 0x82, 0xf2, 0x54, 0x39, 0xd5, 0xbe, 0x24, 0x92, 0x77, 0x6c, 0x6c,
  0xad, 0xa5, 0x34, 0x90, 0xa7, 0xb4, 0x62, 0x05, 0xe2, 0x17, 0xc9, 0x14, 0x5d, 0x9c, 0x5c, 0x16,
  0xb2, 0x03, 0x90, 0x2d, 0xa2, 0xfc, 0x6e, 0xb4, 0x6b, 0x51, 0x32, 0x0f, 0x85, 0xd1, 0xa5, 0x55,
  0xa8, 0x9c, 0xa4, 0xbe, 0x15, 0x50, 0x26, 0xa8, 0x6b, 0xa9, 0xd5, 0x17, 0x97, 0xcc, 0x30, 0x21,
  0x04, 0x53, 0x60, 0x42, 0xc4, 0x34, 0x40, 0x42, 0x2a, 0x73, 0x65, 0xdc, 0xc2, 0x20, 0x7d, 0xa5,
  0xa1, 0x56, 0xf4, 0x03, 0x78, 0xf8, 0xf1, 0x69, 0x3e, 0xf8, 0x4b, 0x03, 0x0c, 0x74, 0xc1, 0x15,
  0x1f, 0xfb, 0x28, 0x71, 0x58, 0xe5, 0xe0, 0xce, 0x7f, 0x61, 0x7e, 0xa1, 0xd1, 0x2e, 0x6b, 0x91,
  0x8e, 0x67, 0xb2, 0x4f, 0xc2, 0xb1, 0x34, 0xa2, 0x32, 0x64, 0x8a, 0x79, 0x9c, 0x4c, 0x06, 0x14,
  0x58, 0xa3, 0x98, 0xde, 0x3b, 0xc3, 0x9b, 0x0f, 0xed, 0xe6, 0xc8, 0xa3, 0x4f, 0x20, 0x33, 0x2f,
  0xbe, 0xb3, 0x1a, 0x97, 0x25, 0x4b, 0x21, 0x4a, 0x63, 0x85, 0xba, 0x6b, 0x6c, 0xa6, 0x62, 0x8d,
  0x45, 0xb9, 0xa9, 0xfa, 0x24, 0x46, 0xa2, 0x69, 0x5a, 0x31, 0x4e, 0x2c, 0x36, 0xac, 0xe0, 0xe7,
  0x4e, 0xc5, 0xc9, 0x16, 0x08, 0x0c, 0xe4, 0x74, 0xe2, 0x77, 0x95, 0xc8, 0x72, 0x60, 0x41, 0x71,
  0x26, 0x70, 0xdf, 0x7d, 0xf7, 0x21, 0x22, 0xe8, 0x8c, 0x8f, 0xb4, 0xba, 0x26, 0x69, 0xb5, 0x27,
  0x36, 0xd5, 0x4c, 0x93, 0x40, 0x4a, 0x12, 0xc9, 0x9a, 0x12, 0x29, 0x01, 0x2b, 0x68, 0xad, 0x28,
  0x0e, 0x67, 0x71, 0x7d, 0x8b, 0x76, 0x84, 0xc6, 0xdc, 0x3c, 0xd9, 0x1e, 0x67, 0x29, 0x02, 0x51,
  0x4b, 0xb4, 0x2c, 0x02, 0x46, 0x54, 0x59, 0xa3, 0x33, 0x6f, 0x47, 0x35, 0xb3, 0x74, 0xb2, 0x69,
  0x40, 0x78, 0x14, 0xe2, 0x29, 0xc4, 0x2e, 0xd0, 0x95, 0x37, 0xe4, 0x3c, 0x4b, 0xb9, 0x11, 0x33,
  0x1b, 0x42, 0x35, 0x9d, 0x3e, 0x54, 0xd0, 0x3c, 0xb0, 0xc7, 0x63, 0xeb, 0x65, 0xff, 0x8d, 0x3f,
  0xf9, 0xe6, 0x9f, 0xa2, 0xbd, 0x2c, 0x6b, 0xce, 0xb9, 0x84, 0xbe, 0x81, 0x7c, 0x42, 0x1c, 0xe9,
  0xa8, 0xd3, 0x5b, 0xd9, 0xbe, 0x03, 0x80, 0x2c, 0x7d, 0x2a, 0x47, 0x98, 0x3f, 0x52, 0xa5, 0x7b,
  0xc8, 0xa7, 0xd0, 0xe7, 0xe2, 0x75, 0x79, 0x29, 0xb3, 0x4e, 0x0f, 0xd9, 0xb4, 0xe2, 0x4f, 0xe7,
  0xcf, 0x7c, 0x1c, 0xe9, 0x29, 0x37, 0x53, 0x1a, 0xde, 0x17, 0x2e, 0x8e, 0xe3, 0x17, 0x7d, 0xa4,
  0x89, 0x56, 0x35, 0x37, 0x88, 0x11, 0xd1, 0x38, 0x85, 0x2c, 0x03, 0xde, 0x14, 0xe5, 0xfa, 0x34,
  0x13, 0x01, 0xc4, 0x31, 0x04, 0x59, 0x18, 0xbb, 0x60, 0x88, 0x1f, 0x7f, 0x6b, 0x9c, 0x75, 0x1f,
  0xb9, 0x0b, 0xaf, 0x6f, 0x03, 0xc5, 0x91, 0xb3, 0xd8, 0x96, 0x7f, 0x91, 0xe2, 0xf0, 0x00, 0x56,
  0xc2, 0xa5, 0xf5, 0x7a, 0x53, 0x1b, 0x9d, 0x0d, 0x0a, 0xda, 0xc0, 0x94, 0x08, 0xb5, 0xb9, 0x00,
  0xbf, 0xe0, 0x24, 0xa6, 0xeb, 0x24, 0xb4, 0x6a, 0x09, 0xd8, 0x4e, 0x2b, 0x6d, 0x6b, 0x64, 0xaa,
  0x5a, 0xb3, 0xe3, 0xfa, 0x8c, 0xb1, 0x17, 0xa6, 0xc3, 0x9a, 0x4c, 0xb4, 0x66, 0x34, 0x4f, 0x2d,
  0x1d, 0x36, 0x88, 0x88, 0xa2, 0x2c, 0xeb, 0xbb, 0x67, 0x58, 0x19, 0xbf, 0xc4, 0xc2, 0xf4, 0x24,
  0x93, 0xb3, 0xd3, 0xec, 0x9b, 0x8d, 0xa8, 0x14, 0x0b, 0x6c, 0xdd, 0xa6, 0xc9, 0x1e, 0xb1, 0x6c,
  0x39, 0xf7, 0x26, 0xfa, 0x2a, 0xa3, 0x7c, 0xb8, 0xf7, 0x27, 0x88, 0xe3, 0x22, 0x4d, 0x9e, 0xd5,
  0xe4, 0x5e, 0x36, 0x46, 0x59, 0x83, 0xb2, 0x29, 0x53, 0xee, 0x04, 0x63, 0x0d, 0x41, 0x25, 0xa0,
  0x3a, 0x55, 0xa7, 0x6b, 0x28, 0x93, 0x36, 0x33, 0x3a, 0xb5, 0x65, 0x9b, 0x36, 0x94, 0x6a, 0x44,
  0x5a, 0x66, 0x25, 0x22, 0xc7, 0x8e, 0x1f, 0x57, 0x6f, 0x39, 0x9f, 0x1c, 0x2b, 0x69, 0x6b, 0x38,
  0xb7, 0x7b, 0x38, 0x73, 0x16, 0xa2, 0xdb, 0x6d, 0x4d, 0xad, 0x31, 0xa1, 0xa1, 0x3e, 0x5d, 0x27,
  0x8a, 0x72, 0x0c, 0xac, 0x58, 0xc3, 0x5a, 0xbd, 0x97, 0xc1, 0xa3, 0x0f, 0xe0, 0x4c, 0xbe, 0x40,
  0x50, 0x99, 0x60, 0xb6, 0x6a, 0xe9, 0x1a, 0x1d, 0x24, 0xbf, 0x26, 0x4b, 0xed, 0xd8, 0xd3, 0x9c,
  0x2b, 0xb7, 0xf3, 0x89, 0xff, 0xec, 0x63, 0x95, 0xea, 0x28, 0xb2, 0x4c, 0xda, 0x59, 0x31, 0xed,
  0x96, 0x50, 0xab, 0x49, 0x17, 0x23, 0x51, 0x9d, 0x60, 0xbe, 0x4e, 0x65, 0xaa, 0x46, 0xff, 0x68,
  0x1e, 0xc7, 0x11, 0xc4, 0x9a, 0x25, 0xcd, 0x3c, 0xac, 0x05, 0x2b, 0xa8, 0x40, 0x20, 0x48, 0xca,
  0x1e, 0x48, 0x2c, 0xa3, 0x11, 0xe8, 0xc7, 0x36, 0xdc, 0x59, 0xfe, 0xae, 0x5b, 0x9e, 0x10, 0xf1,
  0xbb, 0xd5, 0x0f, 0xa3, 0x5a, 0x7c, 0xa5, 0x9b, 0xd5, 0x09, 0x95, 0x68, 0xd6, 0x12, 0x56, 0xf0,
  0xf3, 0x21, 0xbe, 0x3f, 0x89, 0xcc, 0x1e, 0x46, 0x2a, 0x01, 0x03, 0x51, 0xcc, 0xa0, 0xa7, 0x31,
  0x95, 0x7b, 0x98, 0x9e, 0x18, 0xe1, 0x70, 0xe9, 0x6a, 0x82, 0xc6, 0x0a, 0x4e, 0x73, 0x7f, 0xc4,
  0x2f, 0xff, 0xa7, 0x59, 0xac, 0xdb, 0x9f, 0x16, 0x53, 0xd2, 0xd1, 0x98, 0x6b, 0x6a, 0xa1, 0xdd,
  0xd7, 0x52, 0xa6, 0x46, 0xb4, 0x38, 0x4f, 0xa3, 0x96, 0x25, 0x0a, 0x72, 0xf4, 0xae, 0xf0, 0x71,
  0xdc, 0x08, 0x31, 0x1d, 0x16, 0xd1, 0xaa, 0x6f, 0x04, 0x8c, 0x4d, 0x8a, 0x2a, 0x63, 0x9b, 0x14,
  0x1e, 0x63, 0x55, 0x50, 0x6b, 0xb0, 0x1b, 0x94, 0xb8, 0x47, 0x5e, 0x1d, 0x90, 0xd3, 0x2e, 0xaa,
  0x3e, 0x54, 0x9d, 0x0a, 0x83, 0xde, 0x51, 0xd7, 0x4f, 0x9c, 0x53, 0x81, 0xb5, 0x38, 0x9e, 0xc5,
  0x09, 0xe7, 0xb1, 0x13, 0x0b, 0xb0, 0x00, 0x36, 0x48, 0x42, 0xa1, 0xf8, 0x02, 0x7d, 0x9a, 0xc1,
  0xae, 0xc3, 0x2c, 0x2b, 0x3d, 0xc7, 0xba, 0x89, 0x47, 0x29, 0x8c, 0x81, 0xf5, 0x73, 0x48, 0x1c,
  0xa0, 0xb4, 0x4a, 0xc9, 0x66, 0x02, 0x46, 0x68, 0x97, 0xb3, 0xca, 0x54, 0x30, 0x95, 0x59, 0x1a,
  0x0b, 0x31, 0xb1, 0x1e, 0x41, 0xe7, 0x87, 0x28, 0x16, 0xe7, 0x70, 0x64, 0x3e, 0x05, 0x61, 0xdb,
  0x1e, 0x9d, 0x96, 0xc7, 0x88, 0x45, 0xc5, 0xa9, 0x26, 0x4c, 0x3b, 0xb1, 0x5a, 0x2b, 0xfb, 0x67,
  0x66, 0xbd, 0x47, 0x01, 0xdc, 0x8f, 0xfc, 0x78, 0x2f, 0x07, 0x3f, 0x50, 0x9c, 0xae, 0x4e, 0x67,
  0x7e, 0xd0, 0xb3, 0xc2, 0x5c, 0x93, 0x78, 0x5a, 0x47, 0x93, 0xd9, 0x2b, 0x40, 0xc1, 0x49, 0xa8,
  0x79, 0x2c, 0x28, 0xcf, 0x85, 0x42, 0x06, 0x0a, 0x3e, 0xd2, 0xb5, 0x06, 0x72, 0xc3, 0x28, 0x7f,
  0x9a, 0x6a, 0xbd, 0x8c, 0xaa, 0x96, 0xc9, 0x14, 0x32, 0x78, 0x05, 0xbf, 0x1d, 0x5a, 0x34, 0x28,
  0x53, 0x87, 0x70, 0x81, 0xb8, 0x3c, 0x4f, 0x6d, 0xc1, 0x26, 0xaf, 0x20, 0x8a, 0xa3, 0x64, 0xf3,
  0x19, 0xdc, 0xe8, 0x28, 0x04, 0x8b, 0xe0, 0x06, 0xed, 0x28, 0xd7, 0xa1, 0xcd, 0x96, 0x6f, 0x44,
  0x0a, 0x62, 0x69, 0x2d, 0x1b, 0xc7, 0xd0, 0x68, 0xa8, 0xdd, 0x63, 0x77, 0xcf, 0x3d, 0x05, 0x0a,
  0x17, 0xe0, 0xc0, 0x53, 0x9e, 0x19, 0xdd, 0xa6, 0xbe, 0x59, 0x9d, 0x0e, 0xaf, 0x29, 0x0c, 0xa6,
  0xdc, 0x48, 0x00, 0xe5, 0x42, 0xa6, 0x0b, 0xbc, 0x2e, 0x28, 0x0a, 0x0a, 0x05, 0xda, 0x05, 0x2f,
  0x03, 0x8e, 0x87, 0xf2, 0x8b, 0x94, 0x0f, 0xee, 0xa7, 0x36, 0x35, 0x89, 0x58, 0x8b, 0x97, 0xf7,
  0x71, 0x32, 0xe0, 0xc6, 0x0a, 0x31, 0x01, 0x71, 0xb5, 0x4a, 0xb0, 0xb0, 0x88, 0x09, 0x2c, 0xb8,
  0x3d, 0x38, 0xb9, 0x33, 0xf0, 0x96, 0xf5, 0xa3, 0x5c, 0x0f, 0x4f, 0x37, 0x50, 0x8d, 0x23, 0x48,
  0x5c, 0x41, 0x7b, 0x89, 0xe1, 0x4b, 0xb3, 0x9f, 0x2c, 0x69, 0xe3, 0xa1, 0xa9, 0x8d, 0x28, 0xe9,
  0x38, 0x11, 0xb7, 0xc3, 0x95, 0xb1, 0xea, 0xf8, 0xf4, 0x9c, 0x73, 0x3f, 0x28, 0x9e, 0xff, 0x5d,
  0x70, 0xdf, 0xf8, 0x0c, 0xac, 0xff, 0xe6, 0x8c, 0xec, 0x1d, 0x5b, 0xb6, 0xbb, 0x7c, 0x34, 0xbe,
  0xaf, 0x50, 0x72, 0x3f, 0x96, 0xe8, 0x2e, 0x0d, 0x73, 0xda, 0x49, 0x98, 0xac, 0xe7, 0x26, 0x86,
  0xa9, 0x9c, 0x94, 0x6b, 0x18, 0xd4, 0xe2, 0xeb, 0xd4, 0x26, 0x6b, 0xf4, 0xac, 0x1c, 0x48, 0x6a,
  0x0d, 0x2b, 0x44, 0x95, 0x06, 0xc1, 0x6c, 0x05, 0xc1, 0x26, 0x85, 0x4f, 0xbe, 0x17, 0xaf, 0xcb,
  0x07, 0xeb, 0xa2, 0x1c, 0xc1, 0x55, 0x0b, 0x10, 0xd4, 0x92, 0x5e, 0x99, 0x13, 0xa1, 0x33, 0x51,
  0x1a, 0xe6, 0x53, 0x1f, 0xb2, 0xcd, 0xca, 0x32, 0x15, 0x20, 0x16, 0x08, 0x15, 0x84, 0xd2, 0x2a,
  0x6f, 0x8c, 0x81, 0x6a, 0x4d, 0x5e, 0xdc, 0x7e, 0xcf, 0x39, 0x7f, 0x0b, 0x0f, 0x53, 0x7a, 0xdf,
  0xef, 0xa1, 0xe3, 0xd2, 0x46, 0x00, 0xfe, 0xfa, 0x8b, 0x23, 0x55, 0x13, 0x39, 0x77, 0x94, 0xc7,
  0x43, 0x51, 0xca, 0x24, 0x91, 0x43, 0x4c, 0x5a, 0x8b, 0x27, 0xe1, 0x33, 0x29, 0xb8, 0xa2, 0xf4,
  0xf7, 0x08, 0xab, 0x5c, 0x7a, 0x57, 0x14, 0xc9, 0xe4, 0x34, 0x99, 0xbc, 0xc6, 0x2f, 0x38, 0xe4,
  0x7a, 0x5d, 0xf2, 0x25, 0x9f, 0xc2, 0x60, 0x8e, 0x7c, 0x29, 0x87, 0x5f, 0xf0, 0xf0, 0x5c, 0xc1,
  0xf3, 0x02, 0x5c, 0x35, 0x8f, 0x8a, 0x27, 0x51, 0x7a, 0x1e, 0x9d, 0x09, 0x50, 0x6e, 0xd8, 0x06,
  0x61, 0x6d, 0x1b, 0x44, 0xd3, 0x37, 0x8c, 0x41, 0x85, 0xef, 0xd4, 0x46, 0x1c, 0xcb, 0xc4, 0xe4,
  0x94, 0xfb, 0xad, 0xef, 0x1d, 0x7f, 0x38, 0xde, 0xf7, 0xfb, 0xb0, 0xea, 0xb7, 0xbe, 0x83, 0xde,
  0x78, 0xcb, 0x6e, 0x0e, 0xff, 0x0f, 0xf8, 0x32, 0xcf, 0x33, 0x7b, 0x48, 0x76, 0x57, 0xa7, 0xed,
  0x2d, 0x61, 0x25, 0x4a, 0x5e, 0x0b, 0x48, 0xda, 0x50, 0xb0, 0x71, 0xaa, 0x6a, 0xb3, 0xa4, 0xbd,
  0x03, 0xe0, 0xe5, 0x55, 0x0b, 0x18, 0x12, 0xe1, 0xb8, 0x09, 0x81, 0x75, 0x3d, 0xd0, 0xda, 0xa2,
  0x74, 0x84, 0xce, 0x04, 0xe9, 0x08, 0x51, 0x5e, 0x84, 0x72, 0x62, 0x50, 0x69, 0xeb, 0xa8, 0xf3,
  0xb0, 0x3a, 0x41, 0xd8, 0x14, 0x44, 0xa0, 0x20, 0xb0, 0x2d, 0xdf, 0x30, 0x86, 0x78, 0x66, 0xce,
  0xfd, 0xf1, 0xa6, 0x1d, 0xe5, 0x1f, 0x01, 0x34, 0x4a, 0x1b, 0xda, 0x2d, 0xd3, 0x85, 0xb5, 0xbf,
  0x04, 0xc0, 0xd6, 0x3f, 0x3e, 0x5e, 0xaf, 0xcd, 0x98, 0x3f, 0x9f, 0x3f, 0x12, 0x3f, 0x91, 0x24,
  0xb3, 0xb8, 0x0d, 0xa6, 0xd9, 0xbb, 0x6a, 0x6a, 0x47, 0xe2, 0x0e, 0x60, 0x51, 0x5b, 0x63, 0x36,
  0x4c, 0xb5, 0x16, 0xb6, 0xe7, 0x49, 0x94, 0x24, 0x47, 0x96, 0x1e, 0x8c, 0xb4, 0xd6, 0x32, 0x4b,
  0x5a, 0x4b, 0x88, 0x4d, 0x4c, 0xa9, 0xa1, 0x20, 0x68, 0x9b, 0x94, 0xb5, 0x50, 0x5e, 0xe4, 0xb5,
  0xd7, 0x0f, 0x9e, 0x72, 0x2d, 0xc0, 0xae, 0xdf, 0x83, 0x4d, 0xb7, 0xbe, 0xde, 0x06, 0xb2, 0xf1,
  0xc6, 0x47, 0xd9, 0x7d, 0x73, 0xd2, 0x43, 0x3d, 0xe3, 0x96, 0xb9, 0xb7, 0x83, 0xb2, 0xbd, 0x6e,
  0xfe, 0xed, 0x70, 0x4e, 0xa9, 0x64, 0x23, 0x31, 0xa6, 0xad, 0x11, 0x1b, 0x2d, 0x05, 0x61, 0x3b,
  0xcc, 0xae, 0x25, 0x64, 0xe7, 0xf7, 0x71, 0x87, 0x69, 0x46, 0x2d, 0x8d, 0x4a, 0x6b, 0x4e, 0xc7,
  0xb0, 0x36, 0x99, 0x17, 0x5a, 0x54, 0xa0, 0xa0, 0x21, 0x09, 0xa0, 0x34, 0xad, 0xd4, 0xeb, 0x32,
  0xf9, 0xd6, 0x78, 0xee, 0x77, 0x2e, 0xfd, 0xb3, 0x3d, 0x0b, 0x00, 0xa5, 0x8f, 0xde, 0xc6, 0x3b,
  0x5e, 0x2b, 0x6c, 0xbc, 0x63, 0x9a, 0x97, 0x6f, 0x5e, 0x05, 0xc0, 0xaa, 0x6b, 0x7f, 0xfd, 0x99,
  0xea, 0xb4, 0x7c, 0xaa, 0x32, 0x11, 0xc6, 0x09, 0x1d, 0x6f, 0x6e, 0xfc, 0xef, 0x18, 0x9d, 0xfe,
  0xf4, 0x8e, 0xef, 0x92, 0x43, 0x69, 0x1f, 0x4c, 0xdc, 0x36, 0xa9, 0xe6, 0x41, 0x45, 0x92, 0x80,
  0xa8, 0xb7, 0x41, 0x00, 0x04, 0x81, 0xcc, 0x1f, 0x3a, 0x9a, 0xfb, 0x83, 0xf3, 0xbe, 0x31, 0xf3,
  0xfc, 0xee, 0x1b, 0xfa, 0x00, 0x38, 0xf5, 0xa3, 0x5f, 0x6c, 0x01, 0x71, 0x3a, 0x69, 0xe4, 0x9f,
  0x3e, 0x3e, 0xcf, 0xce, 0xeb, 0x96, 0xf1, 0x9d, 0x27, 0x1f, 0x61, 0xe0, 0xa7, 0xa5, 0xbd, 0x1b,
  0x2e, 0x0e, 0xa7, 0x9d, 0x8c, 0xf9, 0x90, 0x97, 0x4d, 0xe7, 0x75, 0x30, 0x57, 0xd5, 0x34, 0x01,
  0x75, 0x32, 0x32, 0xd8, 0xd9, 0x69, 0x34, 0xed, 0x17, 0x3e, 0x9d, 0x1d, 0x48, 0x89, 0x97, 0x6a,
  0x42, 0x6c, 0x02, 0xa2, 0xde, 0x01, 0x22, 0x4d, 0x23, 0x41, 0x28, 0x73, 0x87, 0x8e, 0xe4, 0x6e,
  0xd8, 0xb4, 0x63, 0xf6, 0xaf, 0xf6, 0xfc, 0x61, 0x2f, 0x67, 0x6e, 0x7f, 0xe7, 0x0b, 0x51, 0xe7,
  0xc4, 0x0f, 0xbe, 0xf3, 0x64, 0x95, 0x97, 0x6f, 0x2c, 0x71, 0xe5, 0x13, 0x13, 0x36, 0xff, 0x4f,
  0x63, 0x2f, 0x6d, 0xbc, 0x60, 0x7a, 0xde, 0xf5, 0xcc, 0x45, 0x9e, 0x8f, 0x9f, 0x74, 0xc3, 0xe5,
  0x24, 0x54, 0xdc, 0x2e, 0xb5, 0xef, 0xce, 0x46, 0xf4, 0x12, 0xa6, 0x7b, 0x42, 0xa3, 0xbb, 0x09,
  0xc0, 0x58, 0x08, 0x40, 0x35, 0x7d, 0x22, 0x90, 0x16, 0xc5, 0x6a, 0x34, 0xd4, 0xd1, 0x83, 0xe3,
  0xd9, 0xff, 0xb9, 0xf1, 0xae, 0xd9, 0xbf, 0xd8, 0xf7, 0x85, 0x5e, 0xb5, 0xee, 0xf6, 0xf9, 0x77,
  0xf7, 0x7a, 0x7a, 0xf7, 0x17, 0x96, 0xb3, 0xf1, 0xab, 0xc9, 0xdb, 0xd3, 0xb7, 0xee, 0xec, 0xfd,
  0xef, 0x7d, 0x23, 0xdc, 0x92, 0xeb, 0x57, 0x2b, 0xb5, 0xab, 0xd3, 0x74, 0xad, 0xda, 0x04, 0xf3,
  0xc4, 0x06, 0x41, 0x93, 0x72, 0x73, 0x32, 0xc0, 0xd2, 0xe4, 0x17, 0x69, 0x88, 0x15, 0x54, 0x90,
  0x46, 0xa7, 0x50, 0x92, 0xde, 0xae, 0x4d, 0x32, 0x77, 0xb5, 0xaa, 0x5e, 0x3c, 0x38, 0x9e, 0xfd,
  0xec, 0x39, 0xff, 0x67, 0xe6, 0xf1, 0xbd, 0x5f, 0xe8, 0x65, 0xdd, 0x57, 0xe7, 0x7f, 0xce, 0x0b,
  0x03, 0xd7, 0x0f, 0xb0, 0xf9, 0xae, 0xe4, 0xad, 0xd0, 0x6b, 0x5f, 0xec, 0x79, 0xff, 0xe0, 0x69,
  0x72, 0x73, 0xbe, 0x5f, 0x5d, 0xe2, 0xe5, 0x94, 0xa7, 0x1d, 0xdd, 0x6e, 0x21, 0x35, 0x8b, 0xe7,
  0x13, 0x8b, 0x69, 0x78, 0x27, 0x1d, 0xb7, 0x92, 0x0e, 0x20, 0x14, 0x54, 0xa8, 0x92, 0x3c, 0x11,
  0x25, 0x77, 0x55, 0x4c, 0x0c, 0x51, 0xa4, 0xa6, 0xe6, 0xcb, 0x3c, 0xf0, 0xda, 0x81, 0xbe, 0x9b,
  0x3e, 0x74, 0xcf, 0x91, 0x89, 0xd7, 0x6f, 0xea, 0x63, 0xc3, 0xf6, 0xb9, 0xff, 0xe0, 0x15, 0x8e,
  0xeb, 0x86, 0xd9, 0x9c, 0x5e, 0xa4, 0x79, 0xe4, 0xaa, 0xfe, 0xe1, 0xb5, 0x17, 0xc6, 0xbf, 0xdb,
  0x55, 0x52, 0x57, 0x78, 0x79, 0x39, 0x3b, 0x93, 0x07, 0xa5, 0x75, 0xfb, 0xf0, 0x4f, 0xd6, 0xe6,
  0x68, 0x6a, 0x47, 0x52, 0x00, 0x46, 0x52, 0x02, 0x98, 0x68, 0x40, 0x45, 0x80, 0x11, 0x6c, 0x24,
  0x04, 0x91, 0x0e, 0xc3, 0x90, 0xc7, 0xa6, 0x67, 0xf5, 0xf7, 0xd6, 0xdd, 0xbe, 0xf8, 0x97, 0xc9,
  0xfe, 0x83, 0x6c, 0xde, 0x31, 0xf5, 0x8b, 0xb9, 0x54, 0xf3, 0xc6, 0x9d, 0xef, 0x43, 0x55, 0x1e,
  0xe5, 0x8c, 0x5b, 0x13, 0x69, 0x77, 0xdf, 0xd4, 0x77, 0xce, 0xc0, 0xca, 0xf8, 0x57, 0xb2, 0xdd,
  0xea, 0x43, 0x8e, 0x6b, 0x2f, 0xf4, 0x0b, 0xe0, 0x64, 0xd4, 0xd2, 0x66, 0x87, 0x95, 0x56, 0xa1,
  0x89, 0x11, 0xc4, 0x2a, 0x54, 0x2c, 0x10, 0x2b, 0x94, 0x91, 0x94, 0xc9, 0x0a, 0x51, 0x43, 0x08,
  0x23, 0xbd, 0x18, 0xc7, 0x3c, 0x5a, 0xa9, 0xe9, 0x7f, 0x3a, 0x74, 0x34, 0x7f, 0xef, 0x7b, 0xbf,
  0x33, 0x79, 0xec, 0xdd, 0x5e, 0xbd, 0x7a, 0x57, 0xd7, 0x9c, 0x76, 0x5d, 0xb7, 0x9c, 0xcd, 0x3b,
  0xda, 0xb7, 0x0e, 0x5e, 0xbe, 0xbe, 0xef, 0xac, 0x81, 0xd1, 0x68, 0x8b, 0x9b, 0xd1, 0xe7, 0xbb,
  0xae, 0xdd, 0xe6, 0x38, 0x6c, 0xd0, 0xda, 0x16, 0xbd, 0x0c, 0xb8, 0xae, 0x4a, 0x62, 0xbb, 0x55,
  0xad, 0xca, 0x4e, 0x62, 0x88, 0x03, 0x21, 0x0a, 0x04, 0x13, 0xeb, 0xb2, 0x31, 0x6a, 0xa7, 0xb1,
  0x3c, 0xd5, 0x08, 0xd4, 0xb3, 0xe3, 0x13, 0x99, 0x67, 0x2e, 0xf8, 0xf6, 0xcc, 0xf8, 0xc9, 0xee,
  0xc1, 0xfc, 0xc2, 0x81, 0xb4, 0x6e, 0x47, 0x5c, 0x3b, 0xc4, 0xd8, 0xd7, 0x8f, 0xb7, 0xfe, 0xfe,
  0xe1, 0x65, 0x23, 0xc5, 0xd3, 0xb7, 0x2c, 0x9c, 0x5a, 0xec, 0xb7, 0x43, 0x4e, 0xd6, 0x5d, 0xae,
  0x2c, 0x23, 0x62, 0xed, 0x90, 0x42, 0x7a, 0x94, 0x98, 0x0c, 0xa2, 0xb5, 0x18, 0x09, 0xb0, 0x7a,
  0x11, 0xd4, 0x04, 0xc2, 0xdb, 0x51, 0x64, 0x8e, 0xcd, 0x96, 0x9d, 0x89, 0xe7, 0xf7, 0xac, 0x3c,
  0xf4, 0xdb, 0xf7, 0xbf, 0x12, 0xb4, 0xd6, 0xfe, 0x7c, 0x3f, 0x63, 0x7f, 0x34, 0xfb, 0xae, 0x65,
  0xfa, 0x7f, 0x4c, 0xe8, 0x21, 0x04, 0x29, 0xb0, 0x36, 0x16, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
])
'''
const lv_img_dsc_t img_wink_png = {
  .header.always_zero = 0,
  .header.w = 50,
  .header.h = 50,
  .data_size = 5158,
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .data = img_wink_png_map,
};

'''
