-- ccache will be used for faster recompilation
-- Building ESP-IDF components for target esp32s3
Using component placed at E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\chmorgan__esp-audio-player for dependency "chmorgan/esp-audio-player", specified in E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\main\idf_component.yml
Using component placed at E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\chmorgan__esp-libhelix-mp3 for dependency "chmorgan/esp-libhelix-mp3", specified in E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\main\idf_component.yml
Using component placed at E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\espressif__esp-dsp for dependency "espressif/esp-dsp", specified in E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\main\idf_component.yml
Using component placed at E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\lvgl__lvgl for dependency "lvgl/lvgl", specified in E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\main\idf_component.yml
Processing 6 dependencies:
[1/6] chmorgan/esp-audio-player (1.0.7) (E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\chmorgan__esp-audio-player)
[2/6] chmorgan/esp-libhelix-mp3 (1.0.3) (E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\chmorgan__esp-libhelix-mp3)
[3/6] espressif/esp-dsp (1.4.12) (E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\espressif__esp-dsp)
[4/6] espressif/esp_websocket_client (1.5.0)
[5/6] lvgl/lvgl (8.3.11) (E:\APPprj\ESP32-S3-Touch-LCD-1.85C-Test\components\lvgl__lvgl)
[6/6] idf (5.3.1)
-- Project sdkconfig file E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/sdkconfig
Loading defaults file E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/sdkconfig.defaults...
E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/sdkconfig.defaults:48 CONFIG_FLASHMODE_QIO was replaced with CONFIG_ESPTOOLPY_FLASHMODE_QIO 
E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/sdkconfig.defaults:60 CONFIG_ESP32_WIFI_RX_BA_WIN was replaced with CONFIG_ESP_WIFI_RX_BA_WIN 
E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/sdkconfig.defaults:61 CONFIG_ESP32_WIFI_RX_BA_WIN was replaced with CONFIG_ESP_WIFI_RX_BA_WIN 
-- Compiler supported targets: xtensa-esp-elf
-- App "ESP32-S3-Touch-LCD-1.85C-Test" version: 60c099c-dirty
-- Adding linker script E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Component idf::esp_coze will be linked with -Wl,--whole-archive
-- Components: app_trace app_update bootloader bootloader_support bt chmorgan__esp-audio-player chmorgan__esp-libhelix-mp3 cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_coze esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__esp-dsp espressif__esp_websocket_client esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/Espressif/frameworks/esp-idf-v5.3.1/components/app_trace D:/Espressif/frameworks/esp-idf-v5.3.1/components/app_update D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support D:/Espressif/frameworks/esp-idf-v5.3.1/components/bt E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/components/chmorgan__esp-audio-player E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/components/chmorgan__esp-libhelix-mp3 D:/Espressif/frameworks/esp-idf-v5.3.1/components/cmock D:/Espressif/frameworks/esp-idf-v5.3.1/components/console D:/Espressif/frameworks/esp-idf-v5.3.1/components/cxx D:/Espressif/frameworks/esp-idf-v5.3.1/components/driver D:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp-tls D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_adc D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_coex D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/components/esp_coze D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_ana_cmpr D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_cam D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_dac D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_gpio D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_gptimer D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_i2c D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_i2s D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_isp D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_jpeg D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_ledc D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_mcpwm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_parlio D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_pcnt D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_ppa D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_rmt D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdio D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdmmc D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdspi D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_spi D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_touch_sens D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_tsens D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_uart D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_usb_serial_jtag D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_eth D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_event D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_gdbstub D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hid D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_http_client D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_http_server D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_https_ota D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_https_server D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_lcd D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_local_ctrl D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_mm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_netif D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_netif_stack D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_partition D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_phy D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_pm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_psram D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_ringbuf D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_system D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_timer D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_vfs_console D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_wifi D:/Espressif/frameworks/esp-idf-v5.3.1/components/espcoredump E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/components/espressif__esp-dsp E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/managed_components/espressif__esp_websocket_client D:/Espressif/frameworks/esp-idf-v5.3.1/components/esptool_py D:/Espressif/frameworks/esp-idf-v5.3.1/components/fatfs D:/Espressif/frameworks/esp-idf-v5.3.1/components/freertos D:/Espressif/frameworks/esp-idf-v5.3.1/components/hal D:/Espressif/frameworks/esp-idf-v5.3.1/components/heap D:/Espressif/frameworks/esp-idf-v5.3.1/components/http_parser D:/Espressif/frameworks/esp-idf-v5.3.1/components/idf_test D:/Espressif/frameworks/esp-idf-v5.3.1/components/ieee802154 D:/Espressif/frameworks/esp-idf-v5.3.1/components/json D:/Espressif/frameworks/esp-idf-v5.3.1/components/log E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/components/lvgl__lvgl D:/Espressif/frameworks/esp-idf-v5.3.1/components/lwip E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/main D:/Espressif/frameworks/esp-idf-v5.3.1/components/mbedtls D:/Espressif/frameworks/esp-idf-v5.3.1/components/mqtt D:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib D:/Espressif/frameworks/esp-idf-v5.3.1/components/nvs_flash D:/Espressif/frameworks/esp-idf-v5.3.1/components/nvs_sec_provider D:/Espressif/frameworks/esp-idf-v5.3.1/components/openthread D:/Espressif/frameworks/esp-idf-v5.3.1/components/partition_table D:/Espressif/frameworks/esp-idf-v5.3.1/components/perfmon D:/Espressif/frameworks/esp-idf-v5.3.1/components/protobuf-c D:/Espressif/frameworks/esp-idf-v5.3.1/components/protocomm D:/Espressif/frameworks/esp-idf-v5.3.1/components/pthread D:/Espressif/frameworks/esp-idf-v5.3.1/components/sdmmc D:/Espressif/frameworks/esp-idf-v5.3.1/components/soc D:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash D:/Espressif/frameworks/esp-idf-v5.3.1/components/spiffs D:/Espressif/frameworks/esp-idf-v5.3.1/components/tcp_transport D:/Espressif/frameworks/esp-idf-v5.3.1/components/touch_element D:/Espressif/frameworks/esp-idf-v5.3.1/components/ulp D:/Espressif/frameworks/esp-idf-v5.3.1/components/unity D:/Espressif/frameworks/esp-idf-v5.3.1/components/usb D:/Espressif/frameworks/esp-idf-v5.3.1/components/vfs D:/Espressif/frameworks/esp-idf-v5.3.1/components/wear_levelling D:/Espressif/frameworks/esp-idf-v5.3.1/components/wifi_provisioning D:/Espressif/frameworks/esp-idf-v5.3.1/components/wpa_supplicant D:/Espressif/frameworks/esp-idf-v5.3.1/components/xtensa
-- Configuring done
-- Generating done
-- Build files have been written to: E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build
