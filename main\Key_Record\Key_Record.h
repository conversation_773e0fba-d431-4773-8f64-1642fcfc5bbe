#pragma once

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "driver/i2s_std.h"
#include "driver/gpio.h"
#include "TCA9554PWR.h"

// 按键定义
#define KEY_1_PIN           TCA9554_EXIO1  // 按键1对应EXIO1

// 录音配置
#define RECORD_SAMPLE_RATE  16000
#define RECORD_BITS         16
#define RECORD_CHANNELS     1
#define RECORD_BUFFER_SIZE  1024
#define MAX_RECORD_TIME_MS  10000  // 最大录制时间10秒

// 按键状态
typedef enum {
    KEY_STATE_RELEASED = 0,
    KEY_STATE_PRESSED = 1
} key_state_t;

// 录音状态
typedef enum {
    RECORD_STATE_IDLE = 0,
    RECORD_STATE_RECORDING = 1,
    RECORD_STATE_STOPPED = 2
} record_state_t;

// 录音数据结构
typedef struct {
    uint8_t *data;
    size_t size;
    size_t capacity;
} record_data_t;

// 函数声明
esp_err_t key_record_init(void);
void key_record_task(void *parameter);
key_state_t get_key1_state(void);
esp_err_t start_recording(void);
esp_err_t stop_recording(void);
void print_record_data(void);
void key_record_deinit(void);
