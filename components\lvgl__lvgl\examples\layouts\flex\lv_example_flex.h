/**
 * @file lv_example_flex.h
 *
 */

#ifndef LV_EXAMPLE_FLEX_H
#define LV_EXAMPLE_FLEX_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_flex_1(void);
void lv_example_flex_2(void);
void lv_example_flex_3(void);
void lv_example_flex_4(void);
void lv_example_flex_5(void);
void lv_example_flex_6(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_FLEX_H*/
