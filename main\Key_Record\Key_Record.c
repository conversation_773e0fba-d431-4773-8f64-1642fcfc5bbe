#include "Key_Record.h"
#include "PCM5101.h"

static const char *TAG = "KEY_RECORD";

// 全局变量
extern i2s_chan_handle_t i2s_rx_chan; // 使用PCM5101.c中已创建的RX通道
static record_data_t record_data = {0};
static record_state_t current_record_state = RECORD_STATE_IDLE;
static key_state_t last_key_state = KEY_STATE_RELEASED;
static TaskHandle_t key_record_task_handle = NULL;

// 初始化录音功能
esp_err_t key_record_init(void)
{
    ESP_LOGI(TAG, "Initializing key record module");
    
    // 初始化录音数据缓冲区
    record_data.capacity = RECORD_BUFFER_SIZE * (MAX_RECORD_TIME_MS / 100); // 预分配足够的空间
    record_data.data = malloc(record_data.capacity);
    if (!record_data.data) {
        ESP_LOGE(TAG, "Failed to allocate record buffer");
        return ESP_ERR_NO_MEM;
    }
    record_data.size = 0;
    
    // 配置I2S接收通道（用于录音）
    i2s_chan_config_t chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_1, I2S_ROLE_MASTER);
    esp_err_t ret = i2s_new_channel(&chan_cfg, NULL, &rx_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create I2S RX channel: %s", esp_err_to_name(ret));
        free(record_data.data);
        return ret;
    }
    
    // 配置I2S标准模式
    i2s_std_config_t std_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(RECORD_SAMPLE_RATE),
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(RECORD_BITS, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .mclk = GPIO_NUM_NC,
            .bclk = GPIO_NUM_9,   // 根据PCM5101.h中的配置
            .ws = GPIO_NUM_45,
            .dout = GPIO_NUM_NC,
            .din = GPIO_NUM_10,   // 麦克风数据输入
        },
    };
    
    ret = i2s_channel_init_std_mode(rx_handle, &std_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init I2S RX channel: %s", esp_err_to_name(ret));
        i2s_del_channel(rx_handle);
        free(record_data.data);
        return ret;
    }
    
    ESP_LOGI(TAG, "Key record module initialized successfully");
    return ESP_OK;
}

// 获取按键1状态
key_state_t get_key1_state(void)
{
    uint8_t key_value = Read_EXIO(KEY_1_PIN);
    return (key_value == 0) ? KEY_STATE_PRESSED : KEY_STATE_RELEASED; // 假设按下为低电平
}

// 开始录音
esp_err_t start_recording(void)
{
    if (current_record_state != RECORD_STATE_IDLE) {
        ESP_LOGW(TAG, "Recording already in progress");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Starting recording...");
    
    // 重置录音数据
    record_data.size = 0;
    
    // 启用I2S接收通道
    esp_err_t ret = i2s_channel_enable(rx_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable I2S RX channel: %s", esp_err_to_name(ret));
        return ret;
    }
    
    current_record_state = RECORD_STATE_RECORDING;
    ESP_LOGI(TAG, "Recording started");
    return ESP_OK;
}

// 停止录音
esp_err_t stop_recording(void)
{
    if (current_record_state != RECORD_STATE_RECORDING) {
        ESP_LOGW(TAG, "No recording in progress");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Stopping recording...");
    
    // 禁用I2S接收通道
    esp_err_t ret = i2s_channel_disable(rx_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to disable I2S RX channel: %s", esp_err_to_name(ret));
    }
    
    current_record_state = RECORD_STATE_STOPPED;
    ESP_LOGI(TAG, "Recording stopped, recorded %d bytes", record_data.size);
    return ESP_OK;
}

// 打印录音数据
void print_record_data(void)
{
    if (record_data.size == 0) {
        ESP_LOGI(TAG, "No recorded data to print");
        return;
    }
    
    ESP_LOGI(TAG, "=== Recorded Audio Data ===");
    ESP_LOGI(TAG, "Total size: %d bytes", record_data.size);
    ESP_LOGI(TAG, "Duration: %.2f seconds", (float)record_data.size / (RECORD_SAMPLE_RATE * RECORD_CHANNELS * (RECORD_BITS/8)));
    
    // 打印前100个字节的十六进制数据作为示例
    int print_size = (record_data.size > 100) ? 100 : record_data.size;
    ESP_LOGI(TAG, "First %d bytes (hex):", print_size);
    
    for (int i = 0; i < print_size; i += 16) {
        char hex_str[64] = {0};
        char *ptr = hex_str;
        
        for (int j = 0; j < 16 && (i + j) < print_size; j++) {
            ptr += sprintf(ptr, "%02X ", record_data.data[i + j]);
        }
        ESP_LOGI(TAG, "%04X: %s", i, hex_str);
    }
    
    ESP_LOGI(TAG, "=== End of Audio Data ===");
}

// 按键录音任务
void key_record_task(void *parameter)
{
    ESP_LOGI(TAG, "Key record task started");
    
    uint8_t read_buffer[RECORD_BUFFER_SIZE];
    size_t bytes_read = 0;
    
    while (1) {
        // 检查按键状态
        key_state_t current_key_state = get_key1_state();
        
        // 检测按键状态变化
        if (current_key_state != last_key_state) {
            if (current_key_state == KEY_STATE_PRESSED) {
                ESP_LOGI(TAG, "Key 1 pressed - starting recording");
                start_recording();
            } else {
                ESP_LOGI(TAG, "Key 1 released - stopping recording");
                stop_recording();
                print_record_data();
            }
            last_key_state = current_key_state;
        }
        
        // 如果正在录音，读取音频数据
        if (current_record_state == RECORD_STATE_RECORDING) {
            esp_err_t ret = i2s_channel_read(rx_handle, read_buffer, RECORD_BUFFER_SIZE, &bytes_read, 100);
            if (ret == ESP_OK && bytes_read > 0) {
                // 检查缓冲区是否有足够空间
                if (record_data.size + bytes_read <= record_data.capacity) {
                    memcpy(record_data.data + record_data.size, read_buffer, bytes_read);
                    record_data.size += bytes_read;
                } else {
                    ESP_LOGW(TAG, "Record buffer full, stopping recording");
                    stop_recording();
                    print_record_data();
                }
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(10)); // 10ms延迟
    }
}

// 清理资源
void key_record_deinit(void)
{
    ESP_LOGI(TAG, "Deinitializing key record module");
    
    if (key_record_task_handle) {
        vTaskDelete(key_record_task_handle);
        key_record_task_handle = NULL;
    }
    
    if (rx_handle) {
        i2s_channel_disable(rx_handle);
        i2s_del_channel(rx_handle);
        rx_handle = NULL;
    }
    
    if (record_data.data) {
        free(record_data.data);
        record_data.data = NULL;
        record_data.size = 0;
        record_data.capacity = 0;
    }
    
    ESP_LOGI(TAG, "Key record module deinitialized");
}
