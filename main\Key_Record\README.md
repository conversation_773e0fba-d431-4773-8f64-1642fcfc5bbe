# 按键录音功能说明

## 功能描述
本模块实现了按住按键1时录制麦克风音频，松开按键时停止录制并打印录音数据的功能。

## 硬件连接
- **按键1**: 连接到TCA9554PWR的EXIO1引脚
- **麦克风接口**:
  - MIC_WS: GPIO2
  - MIC_SCK: GPIO15  
  - MIC_SD: GPIO39

## 功能特性
- 按下按键1开始录音
- 松开按键1停止录音并打印数据
- 支持16kHz采样率，16位单声道录音
- 最大录制时间10秒
- 实时显示录音统计信息

## 使用方法
1. 编译并烧录程序到ESP32-S3
2. 打开串口监视器查看日志
3. 按住按键1开始录音（会看到"Key 1 pressed - starting recording"）
4. 松开按键1停止录音（会看到录音数据统计信息）

## 输出信息
录音完成后会显示以下信息：
- 录音总大小（字节）
- 采样率、位深度、声道数
- 录音时长
- 音频统计信息（平均幅度、最大/最小值、动态范围）
- 前100字节的十六进制数据

## 配置参数
在`Key_Record.h`中可以修改以下参数：
- `RECORD_SAMPLE_RATE`: 采样率（默认16000Hz）
- `RECORD_BITS`: 位深度（默认16位）
- `RECORD_CHANNELS`: 声道数（默认1，单声道）
- `MAX_RECORD_TIME_MS`: 最大录制时间（默认10秒）

## 注意事项
- 确保麦克风正确连接到指定的GPIO引脚
- 按键1需要配置为输入模式（已在EXIO_Init中配置）
- 录音功能使用I2S_NUM_1，与音频播放功能（I2S_NUM_0）分离
- 录音数据存储在内存中，重启后会丢失

## 故障排除
1. **按键无响应**: 检查TCA9554PWR连接和I2C通信
2. **无录音数据**: 检查麦克风GPIO连接和I2S配置
3. **录音质量差**: 调整采样率或检查麦克风硬件
4. **内存不足**: 减少MAX_RECORD_TIME_MS或RECORD_BUFFER_SIZE
