#include "../lv_demo_music.h"
#if LV_USE_DEMO_MUSIC  && !LV_DEMO_MUSIC_LARGE

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_lv_demo_music_slider_knob_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x08, 0xaf, 0x08, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x08, 0xaf, 0x0b, 0xaf, 0x0c, 0xaf, 0x0c, 0xaf, 0x0c, 0xaf, 0x0c, 0xaf, 0x0c, 0xaf, 0x0b, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0b, 0xaf, 0x0c, 0xaf, 0x0f, 0x93, 0x1c, 0xaf, 0x13, 0xaf, 0x13, 0xaf, 0x13, 0xaf, 0x13, 0xaf, 0x10, 0xaf, 0x10, 0xaf, 0x0f, 0xaf, 0x0c, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0b, 0x77, 0x48, 0x77, 0xb3, 0x77, 0xf4, 0x77, 0xff, 0x77, 0xfc, 0x73, 0xd3, 0x93, 0x74, 0xaf, 0x1c, 0xaf, 0x18, 0xaf, 0x17, 0xaf, 0x14, 0xaf, 0x10, 0xaf, 0x0f, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0b, 0x77, 0x8b, 0x77, 0xff, 0x77, 0xff, 0x77, 0xff, 0x77, 0xff, 0x73, 0xff, 0x73, 0xff, 0x93, 0xff, 0x93, 0xc8, 0xb3, 0x30, 0xaf, 0x1c, 0xaf, 0x1b, 0xaf, 0x17, 0xaf, 0x13, 0xaf, 0x0f, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0x77, 0x7c, 0x77, 0xff, 0x77, 0xff, 0x77, 0xff, 0x77, 0xff, 0x73, 0xff, 0x73, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xcb, 0xaf, 0x28, 0xaf, 0x20, 0xaf, 0x1c, 0xaf, 0x18, 0xaf, 0x13, 0xaf, 0x0f, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0x73, 0x28, 0x77, 0xf7, 0x77, 0xff, 0x77, 0xff, 0x77, 0xff, 0x73, 0xff, 0x97, 0xff, 0x73, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0x83, 0xaf, 0x28, 0xaf, 0x24, 0xaf, 0x1f, 0xaf, 0x18, 0xaf, 0x13, 0xaf, 0x0f, 0xaf, 0x0b, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0x77, 0x80, 0x77, 0xff, 0x77, 0xff, 0x77, 0xff, 0x97, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb7, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0xb3, 0xd3, 0xaf, 0x30, 0xaf, 0x2b, 0xaf, 0x24, 0xaf, 0x1c, 0xaf, 0x17, 0xaf, 0x10, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x08, 0xaf, 0x0c, 0x77, 0xb8, 0x77, 0xff, 0x77, 0xff, 0x73, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0xff, 0x93, 0xff, 0x93, 0xff, 0xb3, 0xfc, 0xaf, 0x3b, 0xaf, 0x30, 0xaf, 0x28, 0xaf, 0x20, 0xaf, 0x1b, 0xaf, 0x14, 0xaf, 0x0f, 0xaf, 0x0b, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x0f, 0x77, 0xc3, 0x77, 0xff, 0x73, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7, 0xff, 0x93, 0xff, 0xb3, 0xff, 0xaf, 0xff, 0xaf, 0x48, 0xaf, 0x37, 0xaf, 0x2f, 0xaf, 0x24, 0xaf, 0x1c, 0xaf, 0x17, 0xaf, 0x10, 0xaf, 0x0b, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x0f, 0x77, 0xa8, 0x77, 0xff, 0x73, 0xff, 0x73, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0xff, 0xb3, 0xff, 0xaf, 0xff, 0xaf, 0xf4, 0xaf, 0x43, 0xaf, 0x3b, 0xaf, 0x30, 0xaf, 0x28, 0xaf, 0x1f, 0xaf, 0x18, 0xaf, 0x10, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x10, 0x73, 0x6b, 0x73, 0xff, 0x73, 0xff, 0x93, 0xff, 0x93, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7, 0xff, 0xb3, 0xff, 0xaf, 0xff, 0xaf, 0xff, 0xaf, 0xc7, 0xaf, 0x44, 0xaf, 0x3c, 0xaf, 0x33, 0xaf, 0x28, 0xaf, 0x20, 0xaf, 0x18, 0xaf, 0x13, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x10, 0xb3, 0x1c, 0x73, 0xe3, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0xb3, 0xff, 0x93, 0xff, 0xb3, 0xff, 0xaf, 0xff, 0xaf, 0xff, 0xaf, 0xff, 0xaf, 0x77, 0xaf, 0x47, 0xaf, 0x3c, 0xaf, 0x33, 0xaf, 0x2b, 0xaf, 0x20, 0xaf, 0x18, 0xaf, 0x13, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x10, 0xaf, 0x17, 0x93, 0x4f, 0x93, 0xf8, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0x93, 0xff, 0xb3, 0xff, 0xaf, 0xff, 0xaf, 0xff, 0xaf, 0xff, 0xaf, 0xab, 0xaf, 0x4c, 0xaf, 0x44, 0xaf, 0x3c, 0xaf, 0x33, 0xaf, 0x28, 0xaf, 0x20, 0xaf, 0x18, 0xaf, 0x13, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x0f, 0xaf, 0x14, 0xaf, 0x1c, 0x93, 0x54, 0x93, 0xe4, 0x93, 0xff, 0x93, 0xff, 0xb3, 0xff, 0xaf, 0xff, 0xaf, 0xff, 0xaf, 0xfb, 0xaf, 0x9f, 0xaf, 0x4c, 0xaf, 0x48, 0xaf, 0x43, 0xaf, 0x38, 0xaf, 0x30, 0xaf, 0x27, 0xaf, 0x1f, 0xaf, 0x17, 0xaf, 0x10, 0xaf, 0x0c, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0f, 0xaf, 0x13, 0xaf, 0x1b, 0xaf, 0x20, 0xaf, 0x30, 0xb3, 0x7b, 0xb3, 0xb7, 0xaf, 0xcb, 0xaf, 0xc3, 0xaf, 0xa0, 0xaf, 0x5f, 0xaf, 0x4b, 0xaf, 0x48, 0xaf, 0x43, 0xaf, 0x3c, 0xaf, 0x34, 0xaf, 0x2c, 0xaf, 0x24, 0xaf, 0x1c, 0xaf, 0x14, 0xaf, 0x10, 0xaf, 0x0b, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x08, 0xaf, 0x0c, 0xaf, 0x10, 0xaf, 0x17, 0xaf, 0x1f, 0xaf, 0x24, 0xaf, 0x2c, 0xaf, 0x34, 0xaf, 0x3b, 0xaf, 0x3f, 0xaf, 0x43, 0xaf, 0x43, 0xaf, 0x43, 0xaf, 0x40, 0xaf, 0x3c, 0xaf, 0x37, 0xaf, 0x2f, 0xaf, 0x28, 0xaf, 0x20, 0xaf, 0x18, 0xaf, 0x13, 0xaf, 0x0f, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x0f, 0xaf, 0x14, 0xaf, 0x18, 0xaf, 0x20, 0xaf, 0x27, 0xaf, 0x2c, 0xaf, 0x33, 0xaf, 0x37, 0xaf, 0x38, 0xaf, 0x3b, 0xaf, 0x3b, 0xaf, 0x37, 0xaf, 0x34, 0xaf, 0x2f, 0xaf, 0x28, 0xaf, 0x23, 0xaf, 0x1b, 0xaf, 0x14, 0xaf, 0x10, 0xaf, 0x0c, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0c, 0xaf, 0x10, 0xaf, 0x14, 0xaf, 0x1b, 0xaf, 0x20, 0xaf, 0x24, 0xaf, 0x2b, 0xaf, 0x2c, 0xaf, 0x2f, 0xaf, 0x30, 0xaf, 0x30, 0xaf, 0x2f, 0xaf, 0x2b, 0xaf, 0x27, 0xaf, 0x23, 0xaf, 0x1c, 0xaf, 0x17, 0xaf, 0x13, 0xaf, 0x0c, 0xaf, 0x0b, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0c, 0xaf, 0x10, 0xaf, 0x14, 0xaf, 0x18, 0xaf, 0x1f, 0xaf, 0x20, 0xaf, 0x24, 0xaf, 0x27, 0xaf, 0x27, 0xaf, 0x27, 0xaf, 0x24, 0xaf, 0x23, 0xaf, 0x1f, 0xaf, 0x1b, 0xaf, 0x17, 0xaf, 0x13, 0xaf, 0x0f, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x0b, 0xaf, 0x0c, 0xaf, 0x10, 0xaf, 0x14, 0xaf, 0x17, 0xaf, 0x1b, 0xaf, 0x1c, 0xaf, 0x1f, 0xaf, 0x1f, 0xaf, 0x1f, 0xaf, 0x1c, 0xaf, 0x1b, 0xaf, 0x18, 0xaf, 0x14, 0xaf, 0x10, 0xaf, 0x0f, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0c, 0xaf, 0x0f, 0xaf, 0x10, 0xaf, 0x13, 0xaf, 0x14, 0xaf, 0x17, 0xaf, 0x17, 0xaf, 0x17, 0xaf, 0x14, 0xaf, 0x14, 0xaf, 0x13, 0xaf, 0x0f, 0xaf, 0x0c, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x0b, 0xaf, 0x0c, 0xaf, 0x0f, 0xaf, 0x0f, 0xaf, 0x10, 0xaf, 0x10, 0xaf, 0x10, 0xaf, 0x0f, 0xaf, 0x0f, 0xaf, 0x0c, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x08, 0xaf, 0x08, 0xaf, 0x0b, 0xaf, 0x0b, 0xaf, 0x0b, 0xaf, 0x0b, 0xaf, 0x0b, 0xaf, 0x0b, 0xaf, 0x08, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x07, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x04, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x03, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00, 0xaf, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0f, 0x1e, 0x84, 0x1c, 0x9e, 0xa3, 0x13, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0b, 0xbf, 0x64, 0x48, 0xdf, 0x5c, 0xb3, 0xdf, 0x5c, 0xf4, 0xbf, 0x64, 0xff, 0x9f, 0x64, 0xfc, 0x7f, 0x6c, 0xd3, 0x5f, 0x74, 0x74, 0x7e, 0xa3, 0x1c, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0b, 0xff, 0x5c, 0x8b, 0xff, 0x54, 0xff, 0xdf, 0x5c, 0xff, 0xbf, 0x64, 0xff, 0x9f, 0x64, 0xff, 0x9f, 0x6c, 0xff, 0x7f, 0x6c, 0xff, 0x5f, 0x74, 0xff, 0x3f, 0x7c, 0xc8, 0xbe, 0x9b, 0x30, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0xff, 0x5c, 0x7c, 0xff, 0x54, 0xff, 0xdf, 0x5c, 0xff, 0xbf, 0x64, 0xff, 0xbf, 0x64, 0xff, 0x9f, 0x6c, 0xff, 0x7f, 0x6c, 0xff, 0x5f, 0x74, 0xff, 0x3f, 0x7c, 0xff, 0x1f, 0x7c, 0xff, 0xfe, 0x83, 0xcb, 0x7e, 0xa3, 0x28, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x9f, 0x6c, 0x28, 0xff, 0x54, 0xf7, 0xdf, 0x5c, 0xff, 0xbf, 0x5c, 0xff, 0xbf, 0x64, 0xff, 0x9f, 0x6c, 0xff, 0x9f, 0x74, 0xff, 0x5f, 0x74, 0xff, 0x3f, 0x7c, 0xff, 0x1f, 0x7c, 0xff, 0xfe, 0x83, 0xff, 0xfe, 0x8b, 0xff, 0xbe, 0x93, 0x83, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0xdf, 0x5c, 0x80, 0xdf, 0x5c, 0xff, 0xbf, 0x5c, 0xff, 0xbf, 0x64, 0xff, 0xdf, 0x74, 0xff, 0x1f, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xf7, 0xff, 0x9f, 0xad, 0xff, 0x1e, 0x84, 0xff, 0xfe, 0x8b, 0xff, 0xde, 0x8b, 0xff, 0xbe, 0x93, 0xd3, 0x5e, 0xab, 0x30, 0x5e, 0xab, 0x2b, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0c, 0xdf, 0x5c, 0xb8, 0xbf, 0x5c, 0xff, 0xbf, 0x64, 0xff, 0x9f, 0x6c, 0xff, 0x9f, 0xc6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0x9c, 0xff, 0xde, 0x8b, 0xff, 0xbe, 0x93, 0xff, 0x9e, 0x9b, 0xfc, 0x5e, 0xa3, 0x3b, 0x5e, 0xab, 0x30, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0f, 0xbf, 0x64, 0xc3, 0xbf, 0x64, 0xff, 0x9f, 0x6c, 0xff, 0x7f, 0x6c, 0xff, 0x5f, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xb5, 0xff, 0xbe, 0x93, 0xff, 0x9e, 0x93, 0xff, 0x7e, 0x9b, 0xff, 0x5e, 0xa3, 0x48, 0x5e, 0xab, 0x37, 0x5e, 0xab, 0x2f, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0f, 0x9f, 0x64, 0xa8, 0x9f, 0x64, 0xff, 0x7f, 0x6c, 0xff, 0x5f, 0x74, 0xff, 0x9f, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xa4, 0xff, 0x9e, 0x93, 0xff, 0x9e, 0x9b, 0xff, 0x7e, 0xa3, 0xf4, 0x5e, 0xab, 0x43, 0x5e, 0xab, 0x3b, 0x5e, 0xab, 0x30, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x10, 0x7f, 0x74, 0x6b, 0x7f, 0x6c, 0xff, 0x5f, 0x74, 0xff, 0x5f, 0x74, 0xff, 0x7f, 0x84, 0xff, 0xff, 0xe6, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0x5f, 0xbd, 0xff, 0x9e, 0x93, 0xff, 0x9e, 0x9b, 0xff, 0x7e, 0xa3, 0xff, 0x5e, 0xa3, 0xc7, 0x5e, 0xab, 0x44, 0x5e, 0xab, 0x3c, 0x5e, 0xab, 0x33, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x10, 0xbe, 0x9b, 0x1c, 0x5f, 0x74, 0xe3, 0x5f, 0x74, 0xff, 0x3f, 0x7c, 0xff, 0x1e, 0x84, 0xff, 0xfe, 0x83, 0xff, 0x3e, 0x94, 0xff, 0xde, 0x93, 0xff, 0x9e, 0x93, 0xff, 0x9e, 0x9b, 0xff, 0x7e, 0xa3, 0xff, 0x5e, 0xa3, 0xff, 0x5e, 0xab, 0x77, 0x5e, 0xab, 0x47, 0x5e, 0xab, 0x3c, 0x5e, 0xab, 0x33, 0x5e, 0xab, 0x2b, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x17, 0x1e, 0x84, 0x4f, 0x3f, 0x7c, 0xf8, 0x1e, 0x84, 0xff, 0xfe, 0x83, 0xff, 0xde, 0x8b, 0xff, 0xbe, 0x93, 0xff, 0xbe, 0x93, 0xff, 0x9e, 0x9b, 0xff, 0x7e, 0xa3, 0xff, 0x5e, 0xa3, 0xff, 0x5e, 0xab, 0xab, 0x5e, 0xab, 0x4c, 0x5e, 0xab, 0x44, 0x5e, 0xab, 0x3c, 0x5e, 0xab, 0x33, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x1c, 0xde, 0x8b, 0x54, 0xfe, 0x83, 0xe4, 0xde, 0x8b, 0xff, 0xbe, 0x93, 0xff, 0xbe, 0x93, 0xff, 0x9e, 0x9b, 0xff, 0x7e, 0x9b, 0xff, 0x5e, 0xa3, 0xfb, 0x5e, 0xab, 0x9f, 0x5e, 0xab, 0x4c, 0x5e, 0xab, 0x48, 0x5e, 0xab, 0x43, 0x5e, 0xab, 0x38, 0x5e, 0xab, 0x30, 0x5e, 0xab, 0x27, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x20, 0x7e, 0xa3, 0x30, 0xbe, 0x93, 0x7b, 0x9e, 0x93, 0xb7, 0x9e, 0x9b, 0xcb, 0x7e, 0xa3, 0xc3, 0x5e, 0xa3, 0xa0, 0x5e, 0xab, 0x5f, 0x5e, 0xab, 0x4b, 0x5e, 0xab, 0x48, 0x5e, 0xab, 0x43, 0x5e, 0xab, 0x3c, 0x5e, 0xab, 0x34, 0x5e, 0xab, 0x2c, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x2c, 0x5e, 0xab, 0x34, 0x5e, 0xab, 0x3b, 0x5e, 0xab, 0x3f, 0x5e, 0xab, 0x43, 0x5e, 0xab, 0x43, 0x5e, 0xab, 0x43, 0x5e, 0xab, 0x40, 0x5e, 0xab, 0x3c, 0x5e, 0xab, 0x37, 0x5e, 0xab, 0x2f, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x27, 0x5e, 0xab, 0x2c, 0x5e, 0xab, 0x33, 0x5e, 0xab, 0x37, 0x5e, 0xab, 0x38, 0x5e, 0xab, 0x3b, 0x5e, 0xab, 0x3b, 0x5e, 0xab, 0x37, 0x5e, 0xab, 0x34, 0x5e, 0xab, 0x2f, 0x5e, 0xab, 0x28, 0x5e, 0xab, 0x23, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x2b, 0x5e, 0xab, 0x2c, 0x5e, 0xab, 0x2f, 0x5e, 0xab, 0x30, 0x5e, 0xab, 0x30, 0x5e, 0xab, 0x2f, 0x5e, 0xab, 0x2b, 0x5e, 0xab, 0x27, 0x5e, 0xab, 0x23, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x20, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x27, 0x5e, 0xab, 0x27, 0x5e, 0xab, 0x27, 0x5e, 0xab, 0x24, 0x5e, 0xab, 0x23, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x1f, 0x5e, 0xab, 0x1c, 0x5e, 0xab, 0x1b, 0x5e, 0xab, 0x18, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x17, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x14, 0x5e, 0xab, 0x13, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x10, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0f, 0x5e, 0xab, 0x0c, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x0b, 0x5e, 0xab, 0x08, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x07, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x04, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x03, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00, 0x5e, 0xab, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0f, 0x84, 0x1e, 0x1c, 0xa3, 0x9e, 0x13, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0b, 0x64, 0xbf, 0x48, 0x5c, 0xdf, 0xb3, 0x5c, 0xdf, 0xf4, 0x64, 0xbf, 0xff, 0x64, 0x9f, 0xfc, 0x6c, 0x7f, 0xd3, 0x74, 0x5f, 0x74, 0xa3, 0x7e, 0x1c, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0b, 0x5c, 0xff, 0x8b, 0x54, 0xff, 0xff, 0x5c, 0xdf, 0xff, 0x64, 0xbf, 0xff, 0x64, 0x9f, 0xff, 0x6c, 0x9f, 0xff, 0x6c, 0x7f, 0xff, 0x74, 0x5f, 0xff, 0x7c, 0x3f, 0xc8, 0x9b, 0xbe, 0x30, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0x5c, 0xff, 0x7c, 0x54, 0xff, 0xff, 0x5c, 0xdf, 0xff, 0x64, 0xbf, 0xff, 0x64, 0xbf, 0xff, 0x6c, 0x9f, 0xff, 0x6c, 0x7f, 0xff, 0x74, 0x5f, 0xff, 0x7c, 0x3f, 0xff, 0x7c, 0x1f, 0xff, 0x83, 0xfe, 0xcb, 0xa3, 0x7e, 0x28, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0x6c, 0x9f, 0x28, 0x54, 0xff, 0xf7, 0x5c, 0xdf, 0xff, 0x5c, 0xbf, 0xff, 0x64, 0xbf, 0xff, 0x6c, 0x9f, 0xff, 0x74, 0x9f, 0xff, 0x74, 0x5f, 0xff, 0x7c, 0x3f, 0xff, 0x7c, 0x1f, 0xff, 0x83, 0xfe, 0xff, 0x8b, 0xfe, 0xff, 0x93, 0xbe, 0x83, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0x5c, 0xdf, 0x80, 0x5c, 0xdf, 0xff, 0x5c, 0xbf, 0xff, 0x64, 0xbf, 0xff, 0x74, 0xdf, 0xff, 0xdf, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff, 0xad, 0x9f, 0xff, 0x84, 0x1e, 0xff, 0x8b, 0xfe, 0xff, 0x8b, 0xde, 0xff, 0x93, 0xbe, 0xd3, 0xab, 0x5e, 0x30, 0xab, 0x5e, 0x2b, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0c, 0x5c, 0xdf, 0xb8, 0x5c, 0xbf, 0xff, 0x64, 0xbf, 0xff, 0x6c, 0x9f, 0xff, 0xc6, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9c, 0x7f, 0xff, 0x8b, 0xde, 0xff, 0x93, 0xbe, 0xff, 0x9b, 0x9e, 0xfc, 0xa3, 0x5e, 0x3b, 0xab, 0x5e, 0x30, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0f, 0x64, 0xbf, 0xc3, 0x64, 0xbf, 0xff, 0x6c, 0x9f, 0xff, 0x6c, 0x7f, 0xff, 0xef, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x3f, 0xff, 0x93, 0xbe, 0xff, 0x93, 0x9e, 0xff, 0x9b, 0x7e, 0xff, 0xa3, 0x5e, 0x48, 0xab, 0x5e, 0x37, 0xab, 0x5e, 0x2f, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0f, 0x64, 0x9f, 0xa8, 0x64, 0x9f, 0xff, 0x6c, 0x7f, 0xff, 0x74, 0x5f, 0xff, 0xce, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa4, 0x5f, 0xff, 0x93, 0x9e, 0xff, 0x9b, 0x9e, 0xff, 0xa3, 0x7e, 0xf4, 0xab, 0x5e, 0x43, 0xab, 0x5e, 0x3b, 0xab, 0x5e, 0x30, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x10, 0x74, 0x7f, 0x6b, 0x6c, 0x7f, 0xff, 0x74, 0x5f, 0xff, 0x74, 0x5f, 0xff, 0x84, 0x7f, 0xff, 0xe6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xbd, 0x5f, 0xff, 0x93, 0x9e, 0xff, 0x9b, 0x9e, 0xff, 0xa3, 0x7e, 0xff, 0xa3, 0x5e, 0xc7, 0xab, 0x5e, 0x44, 0xab, 0x5e, 0x3c, 0xab, 0x5e, 0x33, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x10, 0x9b, 0xbe, 0x1c, 0x74, 0x5f, 0xe3, 0x74, 0x5f, 0xff, 0x7c, 0x3f, 0xff, 0x84, 0x1e, 0xff, 0x83, 0xfe, 0xff, 0x94, 0x3e, 0xff, 0x93, 0xde, 0xff, 0x93, 0x9e, 0xff, 0x9b, 0x9e, 0xff, 0xa3, 0x7e, 0xff, 0xa3, 0x5e, 0xff, 0xab, 0x5e, 0x77, 0xab, 0x5e, 0x47, 0xab, 0x5e, 0x3c, 0xab, 0x5e, 0x33, 0xab, 0x5e, 0x2b, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x17, 0x84, 0x1e, 0x4f, 0x7c, 0x3f, 0xf8, 0x84, 0x1e, 0xff, 0x83, 0xfe, 0xff, 0x8b, 0xde, 0xff, 0x93, 0xbe, 0xff, 0x93, 0xbe, 0xff, 0x9b, 0x9e, 0xff, 0xa3, 0x7e, 0xff, 0xa3, 0x5e, 0xff, 0xab, 0x5e, 0xab, 0xab, 0x5e, 0x4c, 0xab, 0x5e, 0x44, 0xab, 0x5e, 0x3c, 0xab, 0x5e, 0x33, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x1c, 0x8b, 0xde, 0x54, 0x83, 0xfe, 0xe4, 0x8b, 0xde, 0xff, 0x93, 0xbe, 0xff, 0x93, 0xbe, 0xff, 0x9b, 0x9e, 0xff, 0x9b, 0x7e, 0xff, 0xa3, 0x5e, 0xfb, 0xab, 0x5e, 0x9f, 0xab, 0x5e, 0x4c, 0xab, 0x5e, 0x48, 0xab, 0x5e, 0x43, 0xab, 0x5e, 0x38, 0xab, 0x5e, 0x30, 0xab, 0x5e, 0x27, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x20, 0xa3, 0x7e, 0x30, 0x93, 0xbe, 0x7b, 0x93, 0x9e, 0xb7, 0x9b, 0x9e, 0xcb, 0xa3, 0x7e, 0xc3, 0xa3, 0x5e, 0xa0, 0xab, 0x5e, 0x5f, 0xab, 0x5e, 0x4b, 0xab, 0x5e, 0x48, 0xab, 0x5e, 0x43, 0xab, 0x5e, 0x3c, 0xab, 0x5e, 0x34, 0xab, 0x5e, 0x2c, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x2c, 0xab, 0x5e, 0x34, 0xab, 0x5e, 0x3b, 0xab, 0x5e, 0x3f, 0xab, 0x5e, 0x43, 0xab, 0x5e, 0x43, 0xab, 0x5e, 0x43, 0xab, 0x5e, 0x40, 0xab, 0x5e, 0x3c, 0xab, 0x5e, 0x37, 0xab, 0x5e, 0x2f, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x27, 0xab, 0x5e, 0x2c, 0xab, 0x5e, 0x33, 0xab, 0x5e, 0x37, 0xab, 0x5e, 0x38, 0xab, 0x5e, 0x3b, 0xab, 0x5e, 0x3b, 0xab, 0x5e, 0x37, 0xab, 0x5e, 0x34, 0xab, 0x5e, 0x2f, 0xab, 0x5e, 0x28, 0xab, 0x5e, 0x23, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x2b, 0xab, 0x5e, 0x2c, 0xab, 0x5e, 0x2f, 0xab, 0x5e, 0x30, 0xab, 0x5e, 0x30, 0xab, 0x5e, 0x2f, 0xab, 0x5e, 0x2b, 0xab, 0x5e, 0x27, 0xab, 0x5e, 0x23, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x20, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x27, 0xab, 0x5e, 0x27, 0xab, 0x5e, 0x27, 0xab, 0x5e, 0x24, 0xab, 0x5e, 0x23, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x1f, 0xab, 0x5e, 0x1c, 0xab, 0x5e, 0x1b, 0xab, 0x5e, 0x18, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x17, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x14, 0xab, 0x5e, 0x13, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x10, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0f, 0xab, 0x5e, 0x0c, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x0b, 0xab, 0x5e, 0x08, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x07, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x04, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x03, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00, 0xab, 0x5e, 0x00,
#endif
#if LV_COLOR_DEPTH == 32
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0f, 0xf4, 0x80, 0x80, 0x1c, 0xf1, 0x6f, 0x9d, 0x13, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0b, 0xf7, 0x96, 0x5d, 0x48, 0xf8, 0x9a, 0x57, 0xb3, 0xf8, 0x98, 0x59, 0xf4, 0xf7, 0x95, 0x5d, 0xff, 0xf7, 0x92, 0x62, 0xfc, 0xf6, 0x8e, 0x68, 0xd3, 0xf5, 0x88, 0x73, 0x74, 0xf1, 0x6e, 0x9e, 0x1c, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0b, 0xf8, 0x9b, 0x55, 0x8b, 0xf8, 0x9b, 0x54, 0xff, 0xf8, 0x98, 0x59, 0xff, 0xf7, 0x95, 0x5d, 0xff, 0xf7, 0x92, 0x62, 0xff, 0xf6, 0x8f, 0x67, 0xff, 0xf6, 0x8c, 0x6c, 0xff, 0xf5, 0x88, 0x71, 0xff, 0xf5, 0x84, 0x79, 0xc8, 0xf2, 0x73, 0x95, 0x30, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf7, 0x9b, 0x55, 0x7c, 0xf8, 0x9b, 0x54, 0xff, 0xf8, 0x99, 0x58, 0xff, 0xf7, 0x96, 0x5d, 0xff, 0xf7, 0x93, 0x61, 0xff, 0xf6, 0x8f, 0x66, 0xff, 0xf6, 0x8c, 0x6c, 0xff, 0xf6, 0x89, 0x71, 0xff, 0xf5, 0x85, 0x76, 0xff, 0xf5, 0x82, 0x7c, 0xff, 0xf4, 0x7d, 0x83, 0xcb, 0xf0, 0x6c, 0xa2, 0x28, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf6, 0x8f, 0x69, 0x28, 0xf8, 0x9b, 0x54, 0xf7, 0xf8, 0x99, 0x58, 0xff, 0xf7, 0x96, 0x5c, 0xff, 0xf7, 0x93, 0x61, 0xff, 0xf6, 0x90, 0x66, 0xff, 0xf6, 0x91, 0x71, 0xff, 0xf6, 0x89, 0x70, 0xff, 0xf5, 0x85, 0x76, 0xff, 0xf5, 0x82, 0x7b, 0xff, 0xf4, 0x7e, 0x81, 0xff, 0xf4, 0x7b, 0x86, 0xff, 0xf2, 0x74, 0x90, 0x83, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf7, 0x98, 0x5a, 0x80, 0xf8, 0x99, 0x58, 0xff, 0xf7, 0x96, 0x5c, 0xff, 0xf7, 0x93, 0x61, 0xff, 0xf7, 0x98, 0x71, 0xff, 0xfc, 0xdf, 0xd6, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfe, 0xf4, 0xf3, 0xff, 0xf9, 0xaf, 0xab, 0xff, 0xf4, 0x7f, 0x80, 0xff, 0xf4, 0x7b, 0x86, 0xff, 0xf3, 0x78, 0x8b, 0xff, 0xf3, 0x73, 0x91, 0xd3, 0xf0, 0x6a, 0xa5, 0x30, 0xf0, 0x6a, 0xa5, 0x2b, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0c, 0xf8, 0x98, 0x59, 0xb8, 0xf7, 0x96, 0x5c, 0xff, 0xf7, 0x93, 0x60, 0xff, 0xf7, 0x90, 0x65, 0xff, 0xfb, 0xd2, 0xc4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xf5, 0x8d, 0x95, 0xff, 0xf3, 0x78, 0x8a, 0xff, 0xf3, 0x75, 0x90, 0xff, 0xf2, 0x71, 0x95, 0xfc, 0xf0, 0x6a, 0xa4, 0x3b, 0xf0, 0x6a, 0xa5, 0x30, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0f, 0xf7, 0x96, 0x5d, 0xc3, 0xf7, 0x94, 0x60, 0xff, 0xf7, 0x90, 0x65, 0xff, 0xf6, 0x8d, 0x6a, 0xff, 0xfd, 0xea, 0xe5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xa4, 0xb1, 0xff, 0xf3, 0x75, 0x8f, 0xff, 0xf2, 0x72, 0x94, 0xff, 0xf2, 0x6e, 0x99, 0xff, 0xf0, 0x6a, 0xa4, 0x48, 0xf0, 0x6a, 0xa5, 0x37, 0xf0, 0x6a, 0xa5, 0x2f, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0f, 0xf7, 0x92, 0x63, 0xa8, 0xf7, 0x91, 0x64, 0xff, 0xf6, 0x8d, 0x6a, 0xff, 0xf6, 0x8a, 0x6f, 0xff, 0xfb, 0xd0, 0xc8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfd, 0xff, 0xf5, 0x88, 0x9f, 0xff, 0xf2, 0x72, 0x94, 0xff, 0xf2, 0x6f, 0x99, 0xff, 0xf2, 0x6c, 0x9e, 0xf4, 0xf0, 0x6a, 0xa5, 0x43, 0xf0, 0x6a, 0xa5, 0x3b, 0xf0, 0x6a, 0xa5, 0x30, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x10, 0xf6, 0x8c, 0x6d, 0x6b, 0xf6, 0x8e, 0x69, 0xff, 0xf6, 0x8a, 0x6e, 0xff, 0xf5, 0x87, 0x74, 0xff, 0xf6, 0x8c, 0x83, 0xff, 0xfc, 0xde, 0xde, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xf8, 0xf9, 0xff, 0xf8, 0xa9, 0xb9, 0xff, 0xf3, 0x72, 0x94, 0xff, 0xf2, 0x6f, 0x99, 0xff, 0xf2, 0x6c, 0x9d, 0xff, 0xf1, 0x69, 0xa2, 0xc7, 0xf0, 0x6a, 0xa5, 0x44, 0xf0, 0x6a, 0xa5, 0x3c, 0xf0, 0x6a, 0xa5, 0x33, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x10, 0xf1, 0x73, 0x96, 0x1c, 0xf6, 0x8a, 0x6f, 0xe3, 0xf5, 0x87, 0x73, 0xff, 0xf5, 0x84, 0x79, 0xff, 0xf4, 0x80, 0x7e, 0xff, 0xf4, 0x7d, 0x84, 0xff, 0xf4, 0x84, 0x92, 0xff, 0xf3, 0x78, 0x8f, 0xff, 0xf3, 0x72, 0x93, 0xff, 0xf2, 0x6f, 0x98, 0xff, 0xf2, 0x6c, 0x9d, 0xff, 0xf1, 0x69, 0xa1, 0xff, 0xf1, 0x68, 0xa5, 0x77, 0xf0, 0x6a, 0xa5, 0x47, 0xf0, 0x6a, 0xa5, 0x3c, 0xf0, 0x6a, 0xa5, 0x33, 0xf0, 0x6a, 0xa5, 0x2b, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x17, 0xf4, 0x7f, 0x82, 0x4f, 0xf5, 0x84, 0x78, 0xf8, 0xf4, 0x80, 0x7e, 0xff, 0xf4, 0x7d, 0x83, 0xff, 0xf3, 0x79, 0x88, 0xff, 0xf3, 0x76, 0x8e, 0xff, 0xf3, 0x73, 0x93, 0xff, 0xf2, 0x6f, 0x98, 0xff, 0xf2, 0x6c, 0x9d, 0xff, 0xf1, 0x69, 0xa1, 0xff, 0xf1, 0x68, 0xa5, 0xab, 0xf0, 0x6a, 0xa5, 0x4c, 0xf0, 0x6a, 0xa5, 0x44, 0xf0, 0x6a, 0xa5, 0x3c, 0xf0, 0x6a, 0xa5, 0x33, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x1c, 0xf3, 0x79, 0x8a, 0x54, 0xf4, 0x7d, 0x84, 0xe4, 0xf4, 0x7a, 0x88, 0xff, 0xf3, 0x76, 0x8d, 0xff, 0xf3, 0x73, 0x92, 0xff, 0xf2, 0x70, 0x97, 0xff, 0xf2, 0x6d, 0x9c, 0xff, 0xf1, 0x6a, 0xa1, 0xfb, 0xf1, 0x68, 0xa5, 0x9f, 0xf0, 0x6a, 0xa5, 0x4c, 0xf0, 0x6a, 0xa5, 0x48, 0xf0, 0x6a, 0xa5, 0x43, 0xf0, 0x6a, 0xa5, 0x38, 0xf0, 0x6a, 0xa5, 0x30, 0xf0, 0x6a, 0xa5, 0x27, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x20, 0xf1, 0x6d, 0xa0, 0x30, 0xf2, 0x73, 0x93, 0x7b, 0xf3, 0x72, 0x94, 0xb7, 0xf2, 0x6f, 0x98, 0xcb, 0xf2, 0x6d, 0x9d, 0xc3, 0xf1, 0x6a, 0xa1, 0xa0, 0xf0, 0x69, 0xa5, 0x5f, 0xf0, 0x6a, 0xa5, 0x4b, 0xf0, 0x6a, 0xa5, 0x48, 0xf0, 0x6a, 0xa5, 0x43, 0xf0, 0x6a, 0xa5, 0x3c, 0xf0, 0x6a, 0xa5, 0x34, 0xf0, 0x6a, 0xa5, 0x2c, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x2c, 0xf0, 0x6a, 0xa5, 0x34, 0xf0, 0x6a, 0xa5, 0x3b, 0xf0, 0x6a, 0xa5, 0x3f, 0xf0, 0x6a, 0xa5, 0x43, 0xf0, 0x6a, 0xa5, 0x43, 0xf0, 0x6a, 0xa5, 0x43, 0xf0, 0x6a, 0xa5, 0x40, 0xf0, 0x6a, 0xa5, 0x3c, 0xf0, 0x6a, 0xa5, 0x37, 0xf0, 0x6a, 0xa5, 0x2f, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x27, 0xf0, 0x6a, 0xa5, 0x2c, 0xf0, 0x6a, 0xa5, 0x33, 0xf0, 0x6a, 0xa5, 0x37, 0xf0, 0x6a, 0xa5, 0x38, 0xf0, 0x6a, 0xa5, 0x3b, 0xf0, 0x6a, 0xa5, 0x3b, 0xf0, 0x6a, 0xa5, 0x37, 0xf0, 0x6a, 0xa5, 0x34, 0xf0, 0x6a, 0xa5, 0x2f, 0xf0, 0x6a, 0xa5, 0x28, 0xf0, 0x6a, 0xa5, 0x23, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x2b, 0xf0, 0x6a, 0xa5, 0x2c, 0xf0, 0x6a, 0xa5, 0x2f, 0xf0, 0x6a, 0xa5, 0x30, 0xf0, 0x6a, 0xa5, 0x30, 0xf0, 0x6a, 0xa5, 0x2f, 0xf0, 0x6a, 0xa5, 0x2b, 0xf0, 0x6a, 0xa5, 0x27, 0xf0, 0x6a, 0xa5, 0x23, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x20, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x27, 0xf0, 0x6a, 0xa5, 0x27, 0xf0, 0x6a, 0xa5, 0x27, 0xf0, 0x6a, 0xa5, 0x24, 0xf0, 0x6a, 0xa5, 0x23, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x1f, 0xf0, 0x6a, 0xa5, 0x1c, 0xf0, 0x6a, 0xa5, 0x1b, 0xf0, 0x6a, 0xa5, 0x18, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x17, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x14, 0xf0, 0x6a, 0xa5, 0x13, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x10, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0f, 0xf0, 0x6a, 0xa5, 0x0c, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x0b, 0xf0, 0x6a, 0xa5, 0x08, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x07, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x04, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x03, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00, 0xf0, 0x6a, 0xa5, 0x00,
#endif
};

lv_img_dsc_t img_lv_demo_music_slider_knob = {
  .header.always_zero = 0,
  .header.w = 36,
  .header.h = 38,
  .data_size = 1368 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = img_lv_demo_music_slider_knob_map,
};

#endif /*LV_USE_DEMO_MUSIC*/


