/**
 * @file lv_example_ffmpeg.h
 *
 */

#ifndef LV_EXAMPLE_FFMPEG_H
#define LV_EXAMPLE_FFMPEG_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_ffmpeg_1(void);
void lv_example_ffmpeg_2(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_FFMPEG_H*/
