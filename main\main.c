#include "ST77916.h"
#include "PCF85063.h"
#include "SD_MMC.h"
#include "Wireless.h"
#include "TCA9554PWR.h"
#include "BAT_Driver.h"
#include "PCM5101.h"
#include "Key_Record.h"

void Driver_Loop(void *parameter)
{
    Wireless_Init();
    while(1)
    {
        PCF85063_Loop();
        BAT_Get_Volts();
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    vTaskDelete(NULL);
}
void Driver_Init(void)
{
    Flash_Searching();
    BAT_Init();
    I2C_Init();
    EXIO_Init();                    // Example Initialize EXIO
    PCF85063_Init();
    xTaskCreatePinnedToCore(
        Driver_Loop, 
        "Other Driver task",
        4096, 
        NULL, 
        3, 
        NULL, 
        0);
}
void app_main(void)
{
    Driver_Init();

    SD_Init();
    LCD_Init();
    Audio_Init();
    //Play_Music("/sdcard","AAA.mp3");
    LVGL_Init();   // returns the screen object

    // 初始化按键录音功能
    esp_err_t ret = key_record_init();
    if (ret != ESP_OK) {
        ESP_LOGE("MAIN", "Failed to initialize key record: %s", esp_err_to_name(ret));
    } else {
        // 创建按键录音任务
        xTaskCreatePinnedToCore(
            key_record_task,
            "Key Record Task",
            4096,
            NULL,
            2,  // 优先级设置为2，低于驱动任务
            NULL,
            1); // 运行在核心1上
        ESP_LOGI("MAIN", "Key record task created successfully");
    }

    while (1) {
        // raise the task priority of LVGL and/or reduce the handler period can improve the performance
        vTaskDelay(pdMS_TO_TICKS(10));
        // The task running lv_timer_handler should have lower priority than that running `lv_tick_inc`
        lv_timer_handler();
    }
}






