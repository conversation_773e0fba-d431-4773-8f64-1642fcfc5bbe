A simple row and a column layout with flexbox
"""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: layouts/flex/lv_example_flex_1
  :language: c

Arrange items in rows with wrap and even spacing
"""""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: layouts/flex/lv_example_flex_2
  :language: c

Demonstrate flex grow
"""""""""""""""""""""""

.. lv_example:: layouts/flex/lv_example_flex_3
  :language: c

Demonstrate flex grow.
"""""""""""""""""""""""

.. lv_example:: layouts/flex/lv_example_flex_4
  :language: c

Demonstrate column and row gap style properties
"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: layouts/flex/lv_example_flex_5
  :language: c

RTL base direction changes order of the items
"""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: layouts/flex/lv_example_flex_6
  :language: c


